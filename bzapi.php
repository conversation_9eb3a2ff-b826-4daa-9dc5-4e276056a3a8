<?php
header('Content-type:text/html;Charset=utf-8');
error_reporting(-1);
ini_set('display_errors', 1);
// //连接数据库
// $db_connect=mysqli_connect("","","","") or die("Unable to connect to the MySQL!");
// mysqli_query($db_connect,"set character set 'utf8'");

$id=isset($_GET['id'])?intval($_GET['id']):0;
if($id==6666){
    $return_data = array(
        "rel" =>true,
        "data"  =>array(
            'sid'=>$id,
            'filename'  => 'Notepad++32位',
            'downurl'  => 'http://www.notepadplus.com.cn/down/Notepad++.Installer.x32.exe',
            'fullname'=> 'Notepad++32位.exe',
            'description'  => 'Notepad++是一款免费的文本/代码编辑器，支持27种编程语言，支持多文件多视窗编辑。软件安装包体积小，启动速度快。',
            'type'  =>  2,
        ),
    );
    echo json_encode($return_data);
    exit();
} elseif($id==8888){
    $return_data = array(
        "rel" =>true,
        "data"  =>array(
            'sid'=>$id,
            'filename'  => 'Notepad++64位',
            'downurl'  => 'http://www.notepadplus.com.cn/down/Notepad++.Installer.x64.exe',
            'fullname'=> 'Notepad++64位.exe',
            'description'  => 'Notepad++是一款免费的文本/代码编辑器，支持27种编程语言，支持多文件多视窗编辑。软件安装包体积小，启动速度快。',
            'type'  =>  2,
        ),
    );
    echo json_encode($return_data);
    exit();
}else{
    echo json_encode(array('rel'=>false));
    exit();
}