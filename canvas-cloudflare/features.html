<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Better Canvas Features - Dark Mode, Themes & Tools | Better Canvas Extension</title>
    <meta name="description" content="Discover all Better Canvas extension features: dark mode, better canvas themes, GPA calculator, enhanced todo list, card customization, and smart reminders for Canvas LMS.">
    <meta name="keywords" content="better canvas features, better canvas extension, better canvas themes, canvas dark mode, better canvas chrome extension, better canvas firefox, canvas gpa calculator">

    <!-- Favicon and Icons -->
    <link rel="icon" type="image/x-icon" href="images/favicon-16x16.ico" sizes="16x16">
    <link rel="icon" type="image/x-icon" href="images/favicon-32x32.ico" sizes="32x32">
    <link rel="icon" type="image/x-icon" href="images/favicon-48x48.ico" sizes="48x48">
    <link rel="icon" type="image/x-icon" href="images/favicon-64x64.ico" sizes="64x64">
    <link rel="shortcut icon" type="image/x-icon" href="images/favicon-32x32.ico">
    <link rel="apple-touch-icon" sizes="64x64" href="images/favicon-64x64.ico">
    <meta name="msapplication-TileImage" content="images/favicon-64x64.ico">
    <meta name="msapplication-TileColor" content="#2563eb">

    <!-- Open Graph -->
    <meta property="og:title" content="Better Canvas Features - Dark Mode, Themes & Productivity Tools">
    <meta property="og:description" content="Explore all Better Canvas extension features including dark mode, better canvas themes, GPA calculator, and productivity tools for Canvas LMS">
    <meta property="og:type" content="website">

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- 51.la Analytics -->
    <script charset="UTF-8" id="LA_COLLECT" src="//sdk.51.la/js-sdk-pro.min.js"></script>
    <script>LA.init({id:"KuxQaAm5BOlBxQTS",ck:"KuxQaAm5BOlBxQTS",autoTrack:true,screenRecord:true})</script>

    <!-- Custom CSS for responsive images -->
    <style>
        /* 功能截图样式 */
        .feature-screenshot {
            width: 100%;
            height: auto;
            max-height: 400px;
            object-fit: contain;
            border-radius: 0.75rem;
        }

        /* 移动端响应式 */
        @media (max-width: 640px) {
            .feature-screenshot {
                max-height: 300px;
            }
        }
    </style>

    <!-- Structured Data - Features (Simplified) -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebPage",
        "name": "Better Canvas Features",
        "description": "Discover all Better Canvas extension features: dark mode, better canvas themes, GPA calculator, enhanced todo list, card customization, and smart reminders for Canvas LMS."
    }
    </script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                        }
                    }
                }
            }
        }
    </script>

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        body { font-family: 'Inter', system-ui, sans-serif; }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
    </style>

    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-7KZLFW1P0V"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', 'G-7KZLFW1P0V');
    </script>

    <!-- Google AdSense -->
    <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-2379278593682642"
         crossorigin="anonymous"></script>


</head>
<body class="antialiased">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <!-- Logo -->
                <div class="flex items-center">
                    <a href="/" class="flex items-center space-x-2">
                        <div class="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
                            <span class="text-white font-bold text-sm">BC</span>
                        </div>
                        <span class="text-xl font-bold text-gray-900">Better Canvas</span>
                    </a>
                </div>

                <!-- Desktop Navigation -->
                <nav class="hidden md:flex items-center space-x-8">
                    <a href="/" class="text-gray-600 hover:text-gray-900 font-medium transition-colors">Home</a>
                    <a href="download" class="text-gray-600 hover:text-gray-900 font-medium transition-colors">Download</a>
                    <a href="features" class="text-primary-600 font-medium">Features</a>
                    <a href="tutorials" class="text-gray-600 hover:text-gray-900 font-medium transition-colors">Tutorials</a>
                    <a href="themes" class="text-gray-600 hover:text-gray-900 font-medium transition-colors">Themes</a>
                    <a href="faq" class="text-gray-600 hover:text-gray-900 font-medium transition-colors">FAQ</a>
                </nav>

                <!-- CTA Button -->
                <div class="hidden md:flex">
                    <a href="download" class="bg-primary-600 hover:bg-primary-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-200">
                        Download Now
                    </a>
                </div>

                <!-- Mobile menu button -->
                <button class="md:hidden p-2" onclick="toggleMobileMenu()">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                </button>
            </div>

            <!-- Mobile Navigation -->
            <div id="mobile-menu" class="hidden md:hidden py-4 border-t border-gray-200">
                <nav class="flex flex-col space-y-4">
                    <a href="/" class="text-gray-600 hover:text-gray-900 font-medium">Home</a>
                    <a href="download" class="text-gray-600 hover:text-gray-900 font-medium">Download</a>
                    <a href="features" class="text-primary-600 font-medium">Features</a>
                    <a href="tutorials" class="text-gray-600 hover:text-gray-900 font-medium">Tutorials</a>
                    <a href="themes" class="text-gray-600 hover:text-gray-900 font-medium">Themes</a>
                    <a href="faq" class="text-gray-600 hover:text-gray-900 font-medium">FAQ</a>
                </nav>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="py-16 lg:py-24 bg-gradient-to-br from-gray-50 to-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <h1 class="text-4xl lg:text-6xl font-bold text-gray-900 leading-tight mb-6">
                    Better Canvas Extension Features
                </h1>
                <p class="text-xl text-gray-600 mb-8 max-w-3xl mx-auto leading-relaxed">
                    Discover all the powerful features that make Better Canvas the most popular Canvas enhancement extension.
                    From dark mode to better canvas themes, we've got everything you need for a superior Canvas experience.
                </p>

                <!-- Feature Count -->
                <div class="flex flex-col sm:flex-row items-center justify-center space-y-2 sm:space-y-0 sm:space-x-8 mb-12">
                    <div class="flex items-center">
                        <span class="text-2xl font-bold text-primary-600 mr-2">6+</span>
                        <span class="text-gray-600 font-medium">Core Features</span>
                    </div>
                    <div class="text-gray-600">
                        <span class="font-semibold">50+</span> Better Canvas Themes
                    </div>
                    <div class="text-gray-600">
                        <span class="font-semibold">100%</span> Free
                    </div>
                </div>

                <!-- Quick Navigation -->
                <div class="flex flex-wrap justify-center gap-3 mb-8">
                    <a href="#dark-mode" class="bg-white hover:bg-gray-50 text-gray-700 font-medium py-2 px-4 rounded-lg border border-gray-300 transition-colors text-sm">
                        🌙 Dark Mode
                    </a>
                    <a href="#themes" class="bg-white hover:bg-gray-50 text-gray-700 font-medium py-2 px-4 rounded-lg border border-gray-300 transition-colors text-sm">
                        🎨 Themes
                    </a>
                    <a href="#gpa-calculator" class="bg-white hover:bg-gray-50 text-gray-700 font-medium py-2 px-4 rounded-lg border border-gray-300 transition-colors text-sm">
                        📊 GPA Calculator
                    </a>
                    <a href="#todo-list" class="bg-white hover:bg-gray-50 text-gray-700 font-medium py-2 px-4 rounded-lg border border-gray-300 transition-colors text-sm">
                        ✅ Todo List
                    </a>
                    <a href="#customization" class="bg-white hover:bg-gray-50 text-gray-700 font-medium py-2 px-4 rounded-lg border border-gray-300 transition-colors text-sm">
                        🎯 Customization
                    </a>
                    <a href="#reminders" class="bg-white hover:bg-gray-50 text-gray-700 font-medium py-2 px-4 rounded-lg border border-gray-300 transition-colors text-sm">
                        🔔 Reminders
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Dark Mode Feature -->
    <section id="dark-mode" class="py-16 lg:py-24 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid lg:grid-cols-2 gap-12 items-center">
                <!-- Content -->
                <div>
                    <div class="inline-flex items-center bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium mb-4">
                        <span class="text-lg mr-2">🌙</span>
                        Most Popular Feature
                    </div>

                    <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-6">
                        Beautiful Dark Mode for Canvas
                    </h2>

                    <p class="text-xl text-gray-600 mb-6">
                        Transform your Canvas interface with our carefully designed dark mode. Perfect for late-night studying sessions and easier on your eyes during long study periods.
                    </p>

                    <div class="space-y-4 mb-8">
                        <div class="flex items-start space-x-3">
                            <div class="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mt-0.5">
                                <svg class="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900">Reduces Eye Strain</h4>
                                <p class="text-gray-600 text-sm">Easier on your eyes during long study sessions, especially in low-light environments</p>
                            </div>
                        </div>
                        <div class="flex items-start space-x-3">
                            <div class="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mt-0.5">
                                <svg class="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900">Automatic Switching</h4>
                                <p class="text-gray-600 text-sm">Automatically switches based on your system preferences or manual toggle</p>
                            </div>
                        </div>
                        <div class="flex items-start space-x-3">
                            <div class="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mt-0.5">
                                <svg class="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900">Preserves Functionality</h4>
                                <p class="text-gray-600 text-sm">All Canvas features work perfectly with dark mode enabled</p>
                            </div>
                        </div>
                    </div>

                    <div class="flex flex-col sm:flex-row gap-4">
                        <a href="download" class="bg-primary-600 hover:bg-primary-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-200">
                            Try Dark Mode Now
                        </a>
                        <a href="tutorials#dark-mode" class="bg-white hover:bg-gray-50 text-gray-900 font-semibold py-3 px-6 rounded-lg border border-gray-300 transition-colors duration-200">
                            Learn How to Use
                        </a>
                    </div>
                </div>

                <!-- Screenshot -->
                <div class="relative">
                    <div class="bg-gray-900 rounded-2xl shadow-2xl p-6">
                        <div class="flex items-center space-x-2 mb-4">
                            <div class="w-3 h-3 bg-red-500 rounded-full"></div>
                            <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
                            <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                            <div class="ml-4 text-gray-400 text-sm">Canvas - Dark Mode</div>
                        </div>
                        <div class="bg-gray-800 rounded-lg p-2 overflow-hidden">
                            <img
                                src="images/dark.png"
                                alt="Better Canvas Dark Mode Dashboard Screenshot - Enhanced readability with professional dark theme"
                                class="w-full h-auto rounded-lg shadow-lg feature-screenshot"
                                loading="lazy"
                            />
                        </div>
                    </div>
                    <!-- Floating badge -->
                    <div class="absolute -top-4 -right-4 bg-blue-600 text-white px-4 py-2 rounded-full text-sm font-semibold">
                        #1 Feature
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Better Canvas Themes Feature -->
    <section id="themes" class="py-16 lg:py-24 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid lg:grid-cols-2 gap-12 items-center">
                <!-- Screenshot -->
                <div class="relative order-2 lg:order-1">
                    <div class="bg-white rounded-2xl shadow-2xl p-6">
                        <div class="flex items-center justify-between mb-4">
                            <h4 class="font-semibold text-gray-900">Better Canvas Themes</h4>
                            <span class="bg-purple-100 text-purple-800 px-2 py-1 rounded text-xs font-medium">
                                50+ Available
                            </span>
                        </div>

                        <div class="grid grid-cols-2 gap-3">
                            <div class="bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg h-20 flex items-center justify-center">
                                <span class="text-white text-xs font-medium">Purple Dream</span>
                            </div>
                            <div class="bg-gradient-to-br from-blue-500 to-cyan-500 rounded-lg h-20 flex items-center justify-center">
                                <span class="text-white text-xs font-medium">Ocean Blue</span>
                            </div>
                            <div class="bg-gradient-to-br from-green-500 to-emerald-500 rounded-lg h-20 flex items-center justify-center">
                                <span class="text-white text-xs font-medium">Forest Green</span>
                            </div>
                            <div class="bg-gradient-to-br from-orange-500 to-red-500 rounded-lg h-20 flex items-center justify-center">
                                <span class="text-white text-xs font-medium">Sunset</span>
                            </div>
                        </div>

                        <div class="mt-4 pt-4 border-t border-gray-200">
                            <img
                                src="images/50themes.png"
                                alt="Better Canvas 50+ Themes Collection - Theme selection interface with various color schemes"
                                class="w-full h-auto rounded-lg shadow-md feature-screenshot"
                                loading="lazy"
                            />
                        </div>
                    </div>

                    <!-- Floating notification -->
                    <div class="absolute -top-4 -left-4 bg-purple-600 text-white px-4 py-2 rounded-full text-sm font-semibold animate-pulse">
                        Community Favorite
                    </div>
                </div>

                <!-- Content -->
                <div class="order-1 lg:order-2">
                    <div class="inline-flex items-center bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm font-medium mb-4">
                        <span class="text-lg mr-2">🎨</span>
                        Better Canvas Themes
                    </div>

                    <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-6">
                        50+ Beautiful Better Canvas Themes
                    </h2>

                    <p class="text-xl text-gray-600 mb-6">
                        Personalize your Canvas experience with our extensive collection of better canvas themes.
                        From minimalist designs to vibrant colors, find the perfect theme that matches your style.
                    </p>

                    <div class="space-y-4 mb-8">
                        <div class="flex items-start space-x-3">
                            <div class="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mt-0.5">
                                <svg class="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900">Community Created</h4>
                                <p class="text-gray-600 text-sm">Themes designed by students and educators from around the world</p>
                            </div>
                        </div>
                        <div class="flex items-start space-x-3">
                            <div class="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mt-0.5">
                                <svg class="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900">Easy Installation</h4>
                                <p class="text-gray-600 text-sm">One-click installation and switching between themes</p>
                            </div>
                        </div>
                        <div class="flex items-start space-x-3">
                            <div class="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mt-0.5">
                                <svg class="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900">Create Your Own</h4>
                                <p class="text-gray-600 text-sm">Built-in theme creator to design and share your custom themes</p>
                            </div>
                        </div>
                    </div>

                    <div class="flex flex-col sm:flex-row gap-4">
                        <a href="themes" class="bg-purple-600 hover:bg-purple-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-200">
                            Browse All Themes
                        </a>
                        <a href="tutorials#themes" class="bg-white hover:bg-gray-50 text-gray-900 font-semibold py-3 px-6 rounded-lg border border-gray-300 transition-colors duration-200">
                            Learn to Create Themes
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- GPA Calculator Feature -->
    <section id="gpa-calculator" class="py-16 lg:py-24 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid lg:grid-cols-2 gap-12 items-center">
                <!-- Content -->
                <div>
                    <div class="inline-flex items-center bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium mb-4">
                        <span class="text-lg mr-2">📊</span>
                        Academic Success Tool
                    </div>

                    <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-6">
                        Smart GPA Calculator
                    </h2>

                    <p class="text-xl text-gray-600 mb-6">
                        Stay on top of your academic performance with our intelligent GPA calculator.
                        Track your grades, calculate what-if scenarios, and set academic goals with ease.
                    </p>

                    <div class="space-y-4 mb-8">
                        <div class="flex items-start space-x-3">
                            <div class="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mt-0.5">
                                <svg class="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900">Real-time Calculations</h4>
                                <p class="text-gray-600 text-sm">Automatically calculates your GPA as grades are updated in Canvas</p>
                            </div>
                        </div>
                        <div class="flex items-start space-x-3">
                            <div class="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mt-0.5">
                                <svg class="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900">What-If Scenarios</h4>
                                <p class="text-gray-600 text-sm">See how future assignments will affect your final grade</p>
                            </div>
                        </div>
                        <div class="flex items-start space-x-3">
                            <div class="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mt-0.5">
                                <svg class="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900">Grade Tracking</h4>
                                <p class="text-gray-600 text-sm">Visual charts and progress tracking for all your courses</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-8">
                        <div class="flex items-center">
                            <svg class="w-5 h-5 text-green-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                            </svg>
                            <span class="text-green-800 text-sm font-medium">
                                Works with all Canvas grading systems and scales
                            </span>
                        </div>
                    </div>

                    <div class="flex flex-col sm:flex-row gap-4">
                        <a href="download" class="bg-green-600 hover:bg-green-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-200">
                            Start Tracking GPA
                        </a>
                        <a href="tutorials#gpa" class="bg-white hover:bg-gray-50 text-gray-900 font-semibold py-3 px-6 rounded-lg border border-gray-300 transition-colors duration-200">
                            How to Use
                        </a>
                    </div>
                </div>

                <!-- Screenshot -->
                <div class="relative">
                    <div class="bg-white rounded-2xl shadow-2xl p-6">
                        <div class="flex items-center justify-between mb-4">
                            <h4 class="font-semibold text-gray-900">GPA Calculator</h4>
                            <span class="bg-green-100 text-green-800 px-2 py-1 rounded text-xs font-medium">
                                Current: 3.8
                            </span>
                        </div>

                        <div class="space-y-3">
                            <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                                <span class="text-sm font-medium">Mathematics 101</span>
                                <span class="text-sm font-bold text-green-600">A-</span>
                            </div>
                            <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                                <span class="text-sm font-medium">English Literature</span>
                                <span class="text-sm font-bold text-blue-600">B+</span>
                            </div>
                            <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                                <span class="text-sm font-medium">Chemistry Lab</span>
                                <span class="text-sm font-bold text-green-600">A</span>
                            </div>
                        </div>

                        <div class="mt-4 pt-4 border-t border-gray-200">
                            <img
                                src="images/gpa.png"
                                alt="Better Canvas GPA Calculator Interface - Automatic grade calculation and tracking tool"
                                class="w-full h-auto rounded-lg shadow-md feature-screenshot"
                                loading="lazy"
                            />
                        </div>
                    </div>

                    <!-- Floating badge -->
                    <div class="absolute -top-4 -right-4 bg-green-600 text-white px-4 py-2 rounded-full text-sm font-semibold">
                        Academic Tool
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Additional Features Grid -->
    <section id="more-features" class="py-16 lg:py-24 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                    More Better Canvas Extension Features
                </h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    Discover additional productivity tools and customization options that make Better Canvas the complete Canvas enhancement solution.
                </p>
            </div>

            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Enhanced Todo List -->
                <div id="todo-list" class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200">
                    <div class="text-4xl mb-4">✅</div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-3">Enhanced Todo List</h3>
                    <p class="text-gray-600 mb-4 leading-relaxed">
                        Smart task management with priority levels, due date sorting, and progress tracking for all your assignments.
                    </p>
                    <ul class="text-sm text-gray-600 space-y-1 mb-4">
                        <li>• Priority-based sorting</li>
                        <li>• Custom categories</li>
                        <li>• Progress indicators</li>
                        <li>• Quick add functionality</li>
                    </ul>
                    <a href="tutorials#todo" class="text-primary-600 hover:text-primary-700 font-medium inline-flex items-center">
                        Learn More
                        <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </a>
                </div>

                <!-- Card Customization -->
                <div id="customization" class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200">
                    <div class="text-4xl mb-4">🎯</div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-3">Card Customization</h3>
                    <p class="text-gray-600 mb-4 leading-relaxed">
                        Personalize your Canvas dashboard with custom course cards, colors, and layouts that match your workflow.
                    </p>
                    <ul class="text-sm text-gray-600 space-y-1 mb-4">
                        <li>• Custom course colors</li>
                        <li>• Card size options</li>
                        <li>• Layout preferences</li>
                        <li>• Icon customization</li>
                    </ul>
                    <a href="tutorials#customization" class="text-primary-600 hover:text-primary-700 font-medium inline-flex items-center">
                        Learn More
                        <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </a>
                </div>

                <!-- Smart Reminders -->
                <div id="reminders" class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200">
                    <div class="text-4xl mb-4">🔔</div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-3">Smart Reminders</h3>
                    <p class="text-gray-600 mb-4 leading-relaxed">
                        Never miss a deadline with intelligent notifications and reminders for assignments, quizzes, and important dates.
                    </p>
                    <ul class="text-sm text-gray-600 space-y-1 mb-4">
                        <li>• Custom notification timing</li>
                        <li>• Multiple reminder types</li>
                        <li>• Browser notifications</li>
                        <li>• Email integration</li>
                    </ul>
                    <a href="tutorials#reminders" class="text-primary-600 hover:text-primary-700 font-medium inline-flex items-center">
                        Learn More
                        <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </a>
                </div>

                <!-- Quick Access Toolbar -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200">
                    <div class="text-4xl mb-4">⚡</div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-3">Quick Access Toolbar</h3>
                    <p class="text-gray-600 mb-4 leading-relaxed">
                        Access your most-used Canvas features instantly with a customizable floating toolbar.
                    </p>
                    <ul class="text-sm text-gray-600 space-y-1 mb-4">
                        <li>• Customizable shortcuts</li>
                        <li>• Floating interface</li>
                        <li>• One-click actions</li>
                        <li>• Keyboard shortcuts</li>
                    </ul>
                    <a href="tutorials#toolbar" class="text-primary-600 hover:text-primary-700 font-medium inline-flex items-center">
                        Learn More
                        <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </a>
                </div>

                <!-- Performance Optimization -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200">
                    <div class="text-4xl mb-4">🚀</div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-3">Performance Boost</h3>
                    <p class="text-gray-600 mb-4 leading-relaxed">
                        Faster page loading, optimized navigation, and improved Canvas responsiveness for a smoother experience.
                    </p>
                    <ul class="text-sm text-gray-600 space-y-1 mb-4">
                        <li>• Faster page loads</li>
                        <li>• Optimized scripts</li>
                        <li>• Reduced memory usage</li>
                        <li>• Smooth animations</li>
                    </ul>
                    <a href="tutorials#performance" class="text-primary-600 hover:text-primary-700 font-medium inline-flex items-center">
                        Learn More
                        <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </a>
                </div>

                <!-- Accessibility Features -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200">
                    <div class="text-4xl mb-4">♿</div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-3">Accessibility Tools</h3>
                    <p class="text-gray-600 mb-4 leading-relaxed">
                        Enhanced accessibility features including high contrast modes, font size controls, and screen reader improvements.
                    </p>
                    <ul class="text-sm text-gray-600 space-y-1 mb-4">
                        <li>• High contrast themes</li>
                        <li>• Font size controls</li>
                        <li>• Screen reader support</li>
                        <li>• Keyboard navigation</li>
                    </ul>
                    <a href="tutorials#accessibility" class="text-primary-600 hover:text-primary-700 font-medium inline-flex items-center">
                        Learn More
                        <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Final CTA Section -->
    <section class="py-16 lg:py-24 gradient-bg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center text-white">
                <h2 class="text-3xl lg:text-4xl font-bold mb-4">
                    Ready to Experience All Better Canvas Features?
                </h2>
                <p class="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
                    Join 100,000+ students who've already transformed their Canvas experience with Better Canvas extension.
                    Get dark mode, better canvas themes, GPA calculator, and all these powerful features for free.
                </p>

                <!-- CTA Buttons -->
                <div class="flex flex-col sm:flex-row gap-4 justify-center mb-8">
                    <a href="download" class="bg-white text-gray-900 hover:bg-gray-100 font-semibold py-4 px-8 rounded-lg transition-colors duration-200 inline-flex items-center justify-center text-lg">
                        <img src="https://cdn.jsdelivr.net/gh/simple-icons/simple-icons@v9/icons/googlechrome.svg" alt="Chrome" class="w-6 h-6 mr-3">
                        Better Canvas Chrome Extension
                    </a>
                    <a href="download" class="bg-transparent border-2 border-white text-white hover:bg-white hover:text-gray-900 font-semibold py-4 px-8 rounded-lg transition-colors duration-200 inline-flex items-center justify-center text-lg">
                        <img src="https://cdn.jsdelivr.net/gh/simple-icons/simple-icons@v9/icons/firefox.svg" alt="Firefox" class="w-6 h-6 mr-3 filter brightness-0 invert">
                        Better Canvas Firefox
                    </a>
                </div>

                <!-- Feature Highlights -->
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm opacity-90">
                    <div class="flex items-center justify-center">
                        <span class="mr-2">🌙</span>
                        Dark Mode
                    </div>
                    <div class="flex items-center justify-center">
                        <span class="mr-2">🎨</span>
                        50+ Themes
                    </div>
                    <div class="flex items-center justify-center">
                        <span class="mr-2">📊</span>
                        GPA Calculator
                    </div>
                    <div class="flex items-center justify-center">
                        <span class="mr-2">✅</span>
                        Enhanced Tools
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div class="grid md:grid-cols-4 gap-8">
                <!-- Brand -->
                <div class="md:col-span-1">
                    <div class="flex items-center space-x-2 mb-4">
                        <div class="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
                            <span class="text-white font-bold text-sm">BC</span>
                        </div>
                        <span class="text-xl font-bold">Better Canvas</span>
                    </div>
                    <p class="text-gray-400 text-sm leading-relaxed">
                        Transform your Canvas learning experience with Better Canvas extension - featuring dark mode, better canvas themes, GPA calculator, and productivity tools.
                    </p>
                </div>

                <!-- Quick Links -->
                <div>
                    <h3 class="font-semibold mb-4">Quick Links</h3>
                    <ul class="space-y-2 text-sm">
                        <li><a href="download" class="text-gray-400 hover:text-white transition-colors">Download</a></li>
                        <li><a href="features" class="text-gray-400 hover:text-white transition-colors">Features</a></li>
                        <li><a href="themes" class="text-gray-400 hover:text-white transition-colors">Themes</a></li>
                        <li><a href="tutorials" class="text-gray-400 hover:text-white transition-colors">Tutorials</a></li>
                    </ul>
                </div>

                <!-- Support -->
                <div>
                    <h3 class="font-semibold mb-4">Support</h3>
                    <ul class="space-y-2 text-sm">
                        <li><a href="faq" class="text-gray-400 hover:text-white transition-colors">FAQ</a></li>
                        <li><a href="troubleshooting" class="text-gray-400 hover:text-white transition-colors">Troubleshooting</a></li>
                        <li><a href="contact" class="text-gray-400 hover:text-white transition-colors">Contact Us</a></li>
                        <li><a href="changelog" class="text-gray-400 hover:text-white transition-colors">Changelog</a></li>
                    </ul>
                </div>

                <!-- Official Links -->
                <div>
                    <h3 class="font-semibold mb-4">Official Links</h3>
                    <ul class="space-y-2 text-sm">
                        <li><a href="https://chrome.google.com/webstore/detail/better-canvas/cndibmoanboadcifjkjbdpjgfedanolh" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white transition-colors">Chrome Store</a></li>
                        <li><a href="https://addons.mozilla.org/en-US/firefox/addon/better-canvas/" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white transition-colors">Firefox Add-ons</a></li>
                        <li><a href="https://github.com/UseBetterCanvas/bettercanvas" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white transition-colors">GitHub</a></li>
                        <li><a href="https://diditupe.dev/bettercanvas/" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white transition-colors">Official Site</a></li>
                    </ul>
                </div>
            </div>

            <!-- Bottom Bar -->
            <div class="border-t border-gray-800 mt-12 pt-8">
                <div class="flex flex-col md:flex-row justify-between items-center">
                    <div class="text-sm text-gray-400 mb-4 md:mb-0">
                        © 2024 Better Canvas Resources. All rights reserved.
                    </div>
                    <div class="flex space-x-6 text-sm">
                        <a href="privacy" class="text-gray-400 hover:text-white transition-colors">Privacy Policy</a>
                        <a href="terms" class="text-gray-400 hover:text-white transition-colors">Terms of Use</a>
                        <a href="disclaimer" class="text-gray-400 hover:text-white transition-colors">Disclaimer</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <script>
        function toggleMobileMenu() {
            const menu = document.getElementById('mobile-menu');
            menu.classList.toggle('hidden');
        }

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>
</body>
</html>
