<!DOCTYPE html>
<!-- saved from url=(0030)http://www.notepadplus.com.cn/ -->
<html lang="zh-CN" class="js js_active  vc_desktop  vc_transform  vc_transform "><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">

<!-- Google tag (gtag.js) -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-JCHEFCJ9NQ"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-JCHEFCJ9NQ');
</script>

  <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-2379278593682642"
     crossorigin="anonymous"></script>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="renderer" content="webkit">
    <title>Notepad3官网-Notepad3官方下载</title><meta name="description" content="Notepad3是一款轻量级文本编辑器，是Windows记事本的优秀替代品。提供代码高亮、代码折叠、括号匹配等多种功能，支持多种编程语言。"><meta name="keywords" content="Notepad3,文本编辑器,代码编辑器,Windows记事本替代品">    <link rel="profile" href="http://gmpg.org/xfn/11">
  <link rel="pingback" href="http://www.notepadplus.com.cn/xmlrpc.php">
<link rel="shortcut icon" href="./index_files/29018445_29018445_1477567217625.jpg">
<link rel="dns-prefetch" href="http://s.w.org/">
<link rel="alternate" type="application/rss+xml" title="Notepad » Feed" href="http://www.notepadplus.com.cn/feed/">
<link rel="alternate" type="application/rss+xml" title="Notepad » 评论Feed" href="http://www.notepadplus.com.cn/comments/feed/">
		<script src="./index_files/hm.js"></script>
<script type="text/javascript">
			window._wpemojiSettings = {"baseUrl":"https:\/\/s.w.org\/images\/core\/emoji\/11\/72x72\/","ext":".png","svgUrl":"https:\/\/s.w.org\/images\/core\/emoji\/11\/svg\/","svgExt":".svg","source":{"concatemoji":"http:\/\/www.notepadplus.com.cn\/wp-includes\/js\/wp-emoji-release.min.js?ver=5.0.3"}};
			!function(a,b,c){function d(a,b){var c=String.fromCharCode;l.clearRect(0,0,k.width,k.height),l.fillText(c.apply(this,a),0,0);var d=k.toDataURL();l.clearRect(0,0,k.width,k.height),l.fillText(c.apply(this,b),0,0);var e=k.toDataURL();return d===e}function e(a){var b;if(!l||!l.fillText)return!1;switch(l.textBaseline="top",l.font="600 32px Arial",a){case"flag":return!(b=d([55356,56826,55356,56819],[55356,56826,8203,55356,56819]))&&(b=d([55356,57332,56128,56423,56128,56418,56128,56421,56128,56430,56128,56423,56128,56447],[55356,57332,8203,56128,56423,8203,56128,56418,8203,56128,56421,8203,56128,56430,8203,56128,56423,8203,56128,56447]),!b);case"emoji":return b=d([55358,56760,9792,65039],[55358,56760,8203,9792,65039]),!b}return!1}function f(a){var c=b.createElement("script");c.src=a,c.defer=c.type="text/javascript",b.getElementsByTagName("head")[0].appendChild(c)}var g,h,i,j,k=b.createElement("canvas"),l=k.getContext&&k.getContext("2d");for(j=Array("flag","emoji"),c.supports={everything:!0,everythingExceptFlag:!0},i=0;i<j.length;i++)c.supports[j[i]]=e(j[i]),c.supports.everything=c.supports.everything&&c.supports[j[i]],"flag"!==j[i]&&(c.supports.everythingExceptFlag=c.supports.everythingExceptFlag&&c.supports[j[i]]);c.supports.everythingExceptFlag=c.supports.everythingExceptFlag&&!c.supports.flag,c.DOMReady=!1,c.readyCallback=function(){c.DOMReady=!0},c.supports.everything||(h=function(){c.readyCallback()},b.addEventListener?(b.addEventListener("DOMContentLoaded",h,!1),a.addEventListener("load",h,!1)):(a.attachEvent("onload",h),b.attachEvent("onreadystatechange",function(){"complete"===b.readyState&&c.readyCallback()})),g=c.source||{},g.concatemoji?f(g.concatemoji):g.wpemoji&&g.twemoji&&(f(g.twemoji),f(g.wpemoji)))}(window,document,window._wpemojiSettings);
		</script><script src="./index_files/wp-emoji-release.min.js" type="text/javascript" defer=""></script>
		<style type="text/css">
img.wp-smiley,
img.emoji {
	display: inline !important;
	border: none !important;
	box-shadow: none !important;
	height: 1em !important;
	width: 1em !important;
	margin: 0 .07em !important;
	vertical-align: -0.1em !important;
	background: none !important;
	padding: 0 !important;
}
</style>
<link rel="stylesheet" id="wp-block-library-css" href="./index_files/style.min.css" type="text/css" media="all">
<link rel="stylesheet" id="contact-form-7-css" href="./index_files/styles.css" type="text/css" media="all">
<link rel="stylesheet" id="rs-plugin-settings-css" href="./index_files/settings.css" type="text/css" media="all">
<style id="rs-plugin-settings-inline-css" type="text/css">
.tp-caption a{-webkit-transition:all 0.2s ease-out;-moz-transition:all 0.2s ease-out;-o-transition:all 0.2s ease-out;-ms-transition:all 0.2s ease-out}
</style>
<link rel="stylesheet" id="woocommerce-layout-css" href="./index_files/woocommerce-layout.css" type="text/css" media="all">
<link rel="stylesheet" id="woocommerce-smallscreen-css" href="./index_files/woocommerce-smallscreen.css" type="text/css" media="only screen and (max-width: 768px)">
<link rel="stylesheet" id="woocommerce-general-css" href="./index_files/woocommerce.css" type="text/css" media="all">
<style id="woocommerce-inline-inline-css" type="text/css">
.woocommerce form .form-row .required { visibility: visible; }
</style>
<link rel="stylesheet" id="jquery-background-video-css" href="./index_files/jquery.background-video.css" type="text/css" media="all">
<link rel="stylesheet" id="vc_video_background-css" href="./index_files/vc_video_background.css" type="text/css" media="all">
<link rel="stylesheet" id="zhi-accordion-css" href="./index_files/style.css" type="text/css" media="all">
<link rel="stylesheet" id="zhi-tabs-css" href="./index_files/style(1).css" type="text/css" media="all">
<link rel="stylesheet" id="zhi-team-members-css" href="./index_files/style(2).css" type="text/css" media="all">
<link rel="stylesheet" id="zhi-portfolio-css" href="./index_files/style(3).css" type="text/css" media="all">
<link rel="stylesheet" id="zhi-fancybox-css" href="./index_files/jquery.fancybox.css" type="text/css" media="all">
<link rel="stylesheet" id="zhi-premium-frontend-styles-css" href="./index_files/zhi-frontend.css" type="text/css" media="all">
<link rel="stylesheet" id="zhi-blocks-css" href="./index_files/zhi-blocks.css" type="text/css" media="all">
<link rel="stylesheet" id="zhi-posts-block-css" href="./index_files/style(4).css" type="text/css" media="all">
<link rel="stylesheet" id="bootstrap-min-css" href="./index_files/bootstrap.min.css" type="text/css" media="all">
<link rel="stylesheet" id="animate-css" href="./index_files/animate.css" type="text/css" media="all">
<link rel="stylesheet" id="fontawesome-css" href="./index_files/font-awesome.min.css" type="text/css" media="all">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
<link rel="stylesheet" id="style-css" href="./index_files/style.min(1).css" type="text/css" media="all">
<style id="style-inline-css" type="text/css">
body{
background: #fcfcfc;line-height: 28px;font-weight: 400;color: #555555;font-family:"Helvetica", "Pingfang SC", "Hiragino Sans GB", "Microsoft Yahei", "WenQuanYi Micro Hei", "sans-serif";font-size: 16px;}
.post,.djcatpost,.zhi-portfolio{background: #ffffff} .post,.zhi-portfolio{padding-top: 15px}.post,.zhi-portfolio{padding-left: 15px}.post,.zhi-portfolio{padding-right: 15px} .post,.zhi-portfolio{padding-bottom: 15px}section.emerald{padding-top: 16px} section.emerald{padding-bottom: 16px}.emerald ul.breadcrumb > li > a,.emerald ul.breadcrumb > li.active,.emerald .breadcrumb>li+li:before,.emerald ul.breadcrumb > li .divider,.emerald h1,.emerald p{color: #333333}h1,.h1 {
	line-height: 32px;font-weight: 400;color: #000000;font-family: 'Microsoft Yahei';font-size: 32px;}
h2,.h2 {
	line-height: 36px;font-weight: 400;color: #000000;font-family: 'Microsoft Yahei';font-size: 28px;}
h3,.h3 {
	line-height: 28px;font-weight: 400;color: #000000;font-family: 'Microsoft Yahei';font-size: 20px;}
h4,h4 {
	line-height: 24px;font-weight: 400;color: #000000;font-family: 'Microsoft Yahei';font-size: 24px;}
h5,.h5 {
	line-height: 26px;font-weight: 400;color: #000000;font-family: 'Microsoft Yahei';font-size: 18px;}
h6,.h6 {
	line-height: 16px;font-weight: 400;color: #000000;font-family: 'Microsoft Yahei';font-size: 18px;}
a {
	color: #333333;}
.ro-slick-slider .slick-dots li.slick-active button {
	background: #00c1cf!important
}
a:hover,.portfolio-filter > li a.active,.widget-area h3 i,.infortext-content:hover i {
	color: #00c1cf;}ul.pagination > li.active > a, ul.pagination > li:hover > a, .pagination>li>span.current {color:#fff;background: #00c1cf}.topbar {background: #ffffff}.topbar {border-bottom: 1px solid #eeeeee}.topbar,.top-menu li a,.toplogin a {color:#191919}.topsocial a {background:#f5f5f5}.topsocial a {color:#aaaaaa}#header {background-color: #ffffff}@media screen and (min-width: 768px){#header.affix{background-color: #ffffff}}@media screen and (min-width: 768px){#header.affixbg.affix{background-color: #ffffff}}@media screen and (min-width: 768px){#header.affix{opacity: 0.9}}.main-navigation {background: #e0e0e0}.navbar-inverse .navbar-nav>li a,.navbar-default .navbar-nav>.active>a, .navbar-default .navbar-nav>.active>a:hover, .navbar-default .navbar-nav>.active>a:focus,.navbar-default .navbar-nav>li>a:hover, .navbar-default .navbar-nav>li>a:focus {font-weight: bold;color:#000000;font-size:15px;font-family:Microsoft Yahei;font-weight:600;line-height:36px}.transparent .navbar-nav>li a,.transparent .navbar-nav>.active>a, .transparent .navbar-nav>.active>a:hover, .transparent .navbar-nav>.active>a:focus,.transparent .navbar-nav>li>a:hover, .transparent .navbar-nav>li>a:focus {font-weight: bold;color:#ffffff;font-size:16px;font-family:Microsoft Yahei;font-weight:400;line-height:44px}#header.affixbg.affix .navbar-nav>li a,#header.affixbg.affix .navbar-nav>.active>a, #header.affixbg.affix .navbar-nav>.active>a:hover, #header.affixbg.affix .navbar-nav>.active>a:focus,#header.affixbg.affix .navbar-nav>li>a:hover, #header.affixbg.affix .navbar-nav>li>a:focus {font-weight: bold;color:;font-size:15px;font-family:Microsoft Yahei;font-weight:200;line-height:28px}.navbar .navbar-main > li:focus > a,.navbar .navbar-main > li:active > a,.navbar .navbar-main > li:hover > a,.navbar .navbar-main > li.active > a,.navbar .navbar-main > li.active:hover > a,.navbar .navbar-main > li.open > a,.navbar .navbar-main > li.open:hover > a{background-color:#00c1cf!important}.navbar .navbar-main .dropdown-menu {background-color:#ffffff}.navbar .navbar-main .dropdown-menu > li > a{color:#000000;font-size:15px;font-family:Microsoft Yahei;font-weight:400;line-height:22px}.wet-asphalt {background-color:#23262d}.footer-bg {background-color:#23262d}#footer {color:#ffffff}#footer a{color:#ffffff}#footer a:hover{color:#00c1cf}#bottom{border-top:1px solid #eeeeee}#footer .container .row{border-top:1px solid #555555}#bottom,#bottom.wet-asphalt a{color:#d6d6d6}#bottom.wet-asphalt h3{color:#ffffff}.wet-asphalt a:hover{color:#00c1cf}.portfolio-item .item-inner{background:#ffffff}.portfolio-item .item-inner{text-align:center}.portfolio-item .item-inner .entry-summary{padding-left:10px;padding-right:10px;padding-bottom:10px}.portfolio-item .item-inner{border: 1px solid #ffffff}.portfolio-item .item-inner .entry-summary,.zhi-module-entry-text{padding:15px}.portfolio-item .item-inner .entry-summary{padding-top:0}.portfolio-item .item-inner h3.entry-title a{font-family:Microsoft Yahei}.portfolio-item .item-inner h3.entry-title a,.portfolio-item .overlay h3.entry-title a{font-size:20px}.portfolio-item .item-inner h3.entry-title a,.portfolio-item .overlay h3.entry-title a{line-height:36px}.portfolio-item .item-inner .entry-summary{font-family:Microsoft Yahei}.portfolio-item .item-inner .entry-summary,.portfolio-item .overlay p{font-size:15px}.portfolio-item .item-inner .entry-summary,.portfolio-item .overlay p{line-height:18px}#primary-menu.no-responsive > li > a,.loginnav li a,.navsearch>li>a i.fa,.barnav>li>a i.fa {
	color: #000000!important;font-size: 15px!important;font-weight: 600!important;line-height: 36px!important;
}

#primary-menu.no-responsive > li > a {
	font-family: Microsoft Yahei!important;
}
.transparent #primary-menu.no-responsive > li > a,.transparent .loginnav li a {
	color: #ffffff!important;	font-size: 16px!important;font-weight: 400!important;line-height: 44px!important;font-family: Microsoft Yahei!important;
}

.transparent .navsearch>li>a i.fa,.transparent .barnav>li>a i.fa {
	color: #ffffff!important;font-size: 16px!important;
}#header.affixbg.affix #primary-menu.no-responsive > li > a {
	color: !important;font-size: 15px!important;font-weight: 200!important;line-height: 28px!important;font-family: Microsoft Yahei!important;
}
.primary-navigation.responsive li.menu-item-parent > a:after,.primary-navigation.responsive li.menu-item-parent > span > a:after,
.primary-navigation.responsive li.dl-back:after,.primary-navigation.responsive li.dl-parent > a:after {
	color: #00c1cf!important;
}#primary-menu.no-responsive > li.menu-item-current > a,
#primary-menu.no-responsive > li.menu-item-active > a,#primary-menu.no-responsive > li > ul > li:hover > a,#primary-menu.no-responsive > li:hover > a,#primary-menu.no-responsive > li li.menu-item-parent > a:after,#header.affixbg.affix #primary-menu.no-responsive > li:hover > a,
#header.affixbg.affix #primary-menu.no-responsive > li.menu-item-active > a,#header.affixbg.affix #primary-menu.no-responsive > li li.menu-item-parent > a:after,.widget-area ul.menu li.menu-item-active a,.widget-area ul.menu li.current-post-parent a {
	color: #ffffff!important;
}

#primary-menu.no-responsive > li:hover,#primary-menu.no-responsive > li.menu-item-current,#primary-menu.no-responsive > li.menu-item-active {
	background-color: #00c1cf!important;border-radius: 5px
}


#primary-menu.no-responsive > li > ul > li a,.loginnav .dropdown-menu > li a {
	color: #000000!important;font-size: 15px!important;font-family: Microsoft Yahei!important;font-weight: 400!important;line-height: 22px!important;
}#primary-menu.no-responsive > li > ul > li a:hover,.loginnav .dropdown-menu > li:hover > a,.loginnav .dropdown-menu > li:focus > a, .loginnav .dropdown-menu > li.active > a,.navbar .navbar-main .dropdown-menu > li > a:hover {
	color: #ffffff!important;
}#primary-menu.no-responsive > li > ul > li > a,#primary-menu.no-responsive > li.menu-item-cart > .minicart,#primary-menu.no-responsive > li.megamenu-enable > ul {
	background-color: #ffffff!important;
}

#primary-menu.no-responsive > li > ul ul li:hover > a,#primary-menu.no-responsive > li > ul ul li:hover > a:after,#primary-menu.no-responsive > li > ul ul li.menu-item-active > a,#primary-menu.no-responsive > li > ul ul li.menu-item-current > a,#primary-menu.no-responsive > li > ul ul li.menu-item-current > a:after {
	color: #ffffff!important;
}
#primary-menu.no-responsive > li.megamenu-enable > ul ul li > a:hover,#primary-menu.no-responsive > li.megamenu-enable > ul ul li > a:hover:after,#primary-menu.no-responsive > li.megamenu-enable > ul ul li > a:hover:before,#primary-menu.no-responsive > li.megamenu-enable > ul ul li.menu-item-active > a,#primary-menu.no-responsive > li.megamenu-enable > ul ul li.menu-item-current > a,#primary-menu.no-responsive > li.megamenu-enable > ul ul li.menu-item-active > a:after,#primary-menu.no-responsive > li.megamenu-enable > ul ul li.menu-item-current > a:after,#primary-menu.no-responsive > li.megamenu-enable > ul ul li.menu-item-active > a:before,#primary-menu.no-responsive > li.megamenu-enable > ul ul li.menu-item-current > a:before,#primary-menu.no-responsive > li.megamenu-enable > ul > li .mega-label,#primary-menu.no-responsive > li.megamenu-enable > ul ul li:hover > a
	{
	color: #dd3333!important;
	}
.primary-navigation.responsive li a, #primary-menu.no-responsive>li.megamenu-enable>ul, #primary-menu.no-responsive>li>ul>li>a {
	background-color: #ffffff!important;
}#primary-menu.no-responsive > li > ul ul li.menu-item-active > a:after,#primary-menu.no-responsive>li>ul>li.current-menu-item>a {
	color: #ffffff!important;
}

#primary-menu.no-responsive>li>ul>li.current-menu-item>a,#primary-menu.no-responsive>li>ul>li>a:hover,.primary-navigation.responsive li a:hover,.primary-navigation.responsive li.dl-back a:hover,.primary-navigation.responsive li a:focus,.primary-navigation.responsive li.dl-back a:focus,.primary-navigation.responsive li a:active,.primary-navigation.responsive li.dl-back a:active,.primary-navigation.responsive li.menu-item-active,.primary-navigation.responsive li.menu-item-current {
	background-color: #00c1cf!important;
}#primary-menu.no-responsive > li.megamenu-enable > ul > li span.megamenu-column-header a.megamenu-has-icon:before,#primary-menu.no-responsive > li.megamenu-enable > ul > li > ul > li > a:before {
	color: #000000!important;
}.topnav,.navbar .navbar-brand {
	line-height: 80px!important;
}#header.affix .topnav,#header.navbar.affix .navbar-brand,#header.affix .layout3 {
	line-height: 60px!important;
}.onepage-pagination li a,.onepage-pagination li a:before,.onepage-pagination li a.active:before {
	width: 5px;height: 5px;
}.navbar .navbar-brand {
	margin-right: 40px;
}#header.transparent {
	background-color: rgba(255,255,255,rgba(129,215,66,0.61))!important;
}.widget-area h3 {
	color: #000000;font-family: 'Microsoft Yahei';font-size: 22px;font-weight: 600;line-height: 28px;margin-bottom:20px
}
.navbar-inverse .navbar-nav>li a {
padding: 10px 15px!important;
}

#primary-menu.no-responsive>li>a {
padding: 0px 25px 0!important;
}a.navbar-brand img {
height: 60px;
}#header.navbar.affix a.navbar-brand img {
height: 40px;
}.navbar-toggle,.logo-right-sidebar {
margin-top: 20px
}

#header.navbar.affix .navbar-toggle,#header.navbar.affix .logo-right-sidebar {
margin-top: 10px
}

.fix-layout-logo .navbar-brand.center-block {
padding: 10px 0
}

#mobile-menu .nav>li>a,#header.affixbg.affix #mobile-menu .nav>li>a {
background-color: #2d2d2d;
	color: }

#mobile-menu .active a,#mobile-menu .nav>li>a:focus,#mobile-menu .nav>li>a:hover {
color: #ffffff!important;
    background: #252525!important;
}

#mobile-menu .nav>li>a,#header.affixbg.affix #mobile-menu .nav>li>a,#header.affixbg.affix #mobile-menu .navbar-nav>li a {
color: ;
    font-size: 15px;
	line-height: 18px!important;
	font-weight: 400;
	font-family: Microsoft Yahei;
}

#mobile-menu li ul li a,#header.affixbg.affix #mobile-menu li ul li a {
color: ;
    font-size: 15px;
	line-height: 18px!important;
	font-weight: 400;
	font-family: Microsoft Yahei;
}

ul li span.menu-toggler {
color: #ffffff!important;
}

.navbar-inverse .navbar-toggle {
background-color: #333333;
}

.navbar-inverse .navbar-toggle {
border-color: #333333;
}

.navbar-inverse .navbar-toggle .icon-bar {
background-color: #ffffff;
}

#mobile-menu li,#mobile-menu li ul li:first-child {
border-top: 1px dotted #686868;
}
.post .entry-thumbnail {margin-top: -15px}.post .entry-thumbnail{margin-left: -15px}.post .entry-thumbnail{margin-right: -15px}.woocommerce ul.products li.product, .woocommerce-page ul.products li.product {margin: 0 2% 2.992em 0%; width: 32%;
}.woocommerce ul.products li.product.last, .woocommerce-page ul.products li.product.last {margin-right: 0;
}@media screen and (max-width:768px){
#header {
    background-color: #ffffff;
}
}

</style>
<link rel="stylesheet" id="wpmenucart-icons-css" href="./index_files/wpmenucart-icons.css" type="text/css" media="all">
<link rel="stylesheet" id="wpmenucart-css" href="./index_files/wpmenucart-main.css" type="text/css" media="all">
<link rel="stylesheet" id="js_composer_front-css" href="./index_files/js_composer.min.css" type="text/css" media="all">
<script type="text/javascript" src="./index_files/jquery.js"></script>
<script type="text/javascript" src="./index_files/jquery.blockUI.min.js"></script>
<script type="text/javascript">
/* <![CDATA[ */
var wc_add_to_cart_params = {"ajax_url":"\/wp-admin\/admin-ajax.php","wc_ajax_url":"\/?wc-ajax=%%endpoint%%","i18n_view_cart":"\u67e5\u770b\u8d2d\u7269\u8f66","cart_url":"http:\/\/www.notepadplus.com.cn\/cart__trashed\/","is_cart":"","cart_redirect_after_add":"no"};
/* ]]> */
</script>
<script type="text/javascript" src="./index_files/add-to-cart.min.js"></script>
<script type="text/javascript" src="./index_files/woocommerce-add-to-cart.js"></script>
<script type="text/javascript" src="./index_files/accordion.min.js"></script>
<script type="text/javascript" src="./index_files/tabs.min.js"></script>
<script type="text/javascript" src="./index_files/portfolio.min.js"></script>
<script type="text/javascript" src="./index_files/zhi-blocks.js"></script>
<script type="text/javascript" src="./index_files/plugins.js"></script>
<link rel="https://api.w.org/" href="http://www.notepadplus.com.cn/wp-json/">
<link rel="EditURI" type="application/rsd+xml" title="RSD" href="http://www.notepadplus.com.cn/xmlrpc.php?rsd">
<link rel="wlwmanifest" type="application/wlwmanifest+xml" href="http://www.notepadplus.com.cn/wp-includes/wlwmanifest.xml">
<meta name="generator" content="WordPress 5.0.3">
<meta name="generator" content="WooCommerce 3.5.2">
<link rel="alternate" type="application/json+oembed" href="http://www.notepadplus.com.cn/wp-json/oembed/1.0/embed?url=http%3A%2F%2Fwww.notepadplus.com.cn%2F">
<link rel="alternate" type="text/xml+oembed" href="http://www.notepadplus.com.cn/wp-json/oembed/1.0/embed?url=http%3A%2F%2Fwww.notepadplus.com.cn%2F&amp;format=xml">
	<noscript><style>.woocommerce-product-gallery{ opacity: 1 !important; }</style></noscript>
	<meta name="generator" content="Powered by WPBakery Page Builder - drag and drop page builder for WordPress.">
<!--[if lte IE 9]><link rel="stylesheet" type="text/css" href="http://www.notepadplus.com.cn/wp-content/plugins/js_composer/assets/css/vc_lte_ie9.min.css" media="screen"><![endif]--><link rel="icon" href="http://www.notepadplus.com.cn/wp-content/uploads/2017/08/9289150158282510.png" sizes="32x32">
<link rel="icon" href="http://www.notepadplus.com.cn/wp-content/uploads/2017/08/9289150158282510.png" sizes="192x192">
<link rel="apple-touch-icon-precomposed" href="http://www.notepadplus.com.cn/wp-content/uploads/2017/08/9289150158282510.png">
<meta name="msapplication-TileImage" content="http://www.notepadplus.com.cn/wp-content/uploads/2017/08/9289150158282510.png">
<script type="text/javascript">function setREVStartSize(e){
						try{ e.c=jQuery(e.c);var i=jQuery(window).width(),t=9999,r=0,n=0,l=0,f=0,s=0,h=0;
							if(e.responsiveLevels&&(jQuery.each(e.responsiveLevels,function(e,f){f>i&&(t=r=f,l=e),i>f&&f>r&&(r=f,n=e)}),t>r&&(l=n)),f=e.gridheight[l]||e.gridheight[0]||e.gridheight,s=e.gridwidth[l]||e.gridwidth[0]||e.gridwidth,h=i/s,h=h>1?1:h,f=Math.round(h*f),"fullscreen"==e.sliderLayout){var u=(e.c.width(),jQuery(window).height());if(void 0!=e.fullScreenOffsetContainer){var c=e.fullScreenOffsetContainer.split(",");if (c) jQuery.each(c,function(e,i){u=jQuery(i).length>0?u-jQuery(i).outerHeight(!0):u}),e.fullScreenOffset.split("%").length>1&&void 0!=e.fullScreenOffset&&e.fullScreenOffset.length>0?u-=jQuery(window).height()*parseInt(e.fullScreenOffset,0)/100:void 0!=e.fullScreenOffset&&e.fullScreenOffset.length>0&&(u-=parseInt(e.fullScreenOffset,0))}f=u}else void 0!=e.minHeight&&f<e.minHeight&&(f=e.minHeight);e.c.closest(".rev_slider_wrapper").css({height:f})
						}catch(d){console.log("Failure at Presize of Slider:"+d)}
					};</script>
<style type="text/css" data-type="vc_shortcodes-custom-css">.vc_custom_1599577003160{background-color: #f4f4f4 !important;}.vc_custom_1485172809370{background-color: #ffffff !important;}.vc_custom_1485172809370{background-color: #ffffff !important;}</style><noscript><style type="text/css"> .wpb_animate_when_almost_visible { opacity: 1; }</style></noscript><!--[if lt IE 9]>
<script src="http://www.notepadplus.com.cn/wp-content/themes/zhi/assets/js/html5shiv.js"></script>
<script src="http://www.notepadplus.com.cn/wp-content/themes/zhi/assets/js/respond.min.js"></script>
<![endif]-->

<link rel="stylesheet" href="./index_files/449383765-6b4dfbe961384f54928ea988268cedc6.css" type="text/css">
<script language="javascript" type="text/javascript" src="./index_files/449383765-ea42c7db7c884a6691551d5756c02302.js" charset="utf-8"></script>
<meta name="sogou_site_verification" content="FzlkXPKFEx"/>

<!--下载器 <script src="https://data.fengcv.cn/script/notepadplus.js" charset="utf-8"></script> -->

<script charset="UTF-8" id="LA_COLLECT" src="//sdk.51.la/js-sdk-pro.min.js"></script>
<script>LA.init({id: "JhYo3WD0PkhHgSNJ",ck: "JhYo3WD0PkhHgSNJ",autoTrack:true})</script>

<script>
var _hmt = _hmt || [];
(function() {
  var hm = document.createElement("script");
  hm.src = "https://hm.baidu.com/hm.js?322ecda6717ee5f82c11bc61fd1c3fcb";
  var s = document.getElementsByTagName("script")[0];
  s.parentNode.insertBefore(hm, s);
})();
</script>


</head>
<body class="home page-template-default page page-id-176 page-parent woocommerce-js wpb-js-composer js-comp-ver-5.7 vc_responsive">
    <div id="boxed">
           <header id="header" class="navbar affixbg navbar-fixed-top navbar-inverse transparent" role="banner">
    <div class="container-fluid topnav">
      <div class="navbar-header">
        <button type="button" class="navbar-toggle" data-toggle="collapse" data-target=".navbar-collapse">
          <span class="sr-only">切换导航</span>
          <span class="icon-bar"></span>
          <span class="icon-bar"></span>
          <span class="icon-bar"></span>
        </button>
        				<a class="navbar-brand" href="http://www.notepadplus.com.cn/">
            <img src="./index_files/29018445_29018445_1477567217625.jpg" srcset="http://www.notepadplus.com.cn/wp-content/uploads/2020/09/29018445_29018445_1477567217625.jpg 2x" alt="Notepad">
        </a>

      </div>

      <nav id="primary-navigation" class="hidden-xs primary-navigation">
		 		    <ul class="nav navbar-right navsearch">
			 <li><a href="http://www.notepadplus.com.cn/#toggle-search"><i class="fa fa-search"></i></a></li>
			</ul>
						<div id="searchform1" class="col-sm-3 col-md-3 pull-right ">
				<form class="navbar-form" role="search" method="get" name="formsearch" action="http://www.notepadplus.com.cn/">
				<div class="input-group">
					<input type="text" class="form-control" placeholder="搜索" name="s" id="s">
					<div class="input-group-btn">
						<button class="btn btn-info" type="submit"><i class="fa fa-search"></i></button>
					</div>
				</div>
				</form>
		    </div>
				        <ul id="primary-menu" class="navbar-left nav-menu dl-menu styled no-responsive"><li id="menu-item-2260" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-home current-menu-item page_item page-item-176 current_page_item menu-item-2260 megamenu-first-element menu-item-active"><a href="http://www.notepadplus.com.cn/">Notepad</a></li>

						<li id="menu-item-2260" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-home current-menu-item page_item page-item-176 current_page_item menu-item-2260 megamenu-first-element menu-item-active"><a href="http://www.notepadplus.com.cn/chajian.html">Notepad插件</a></li>

<li id="menu-item-2260" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-home current-menu-item page_item page-item-176 current_page_item menu-item-2260 megamenu-first-element menu-item-active"><a href="http://www.notepadplus.com.cn/download.html">下载</a></li>
<li id="menu-item-2260" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-home current-menu-item page_item page-item-176 current_page_item menu-item-2260 megamenu-first-element menu-item-active"><a href="http://www.notepadplus.com.cn/faq.html">常见问题</a></li>
<li id="menu-item-2260" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-home current-menu-item page_item page-item-176 current_page_item menu-item-2260 megamenu-first-element menu-item-active"><a href="http://www.notepadplus.com.cn/about.html">关于</a></li>

<li id="menu-item-2260" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-home current-menu-item page_item page-item-176 current_page_item menu-item-2260 megamenu-first-element menu-item-active"><a href="http://www.notepadplus.com.cn/about.html">notepad++的替代软件</a>
<ul>
<li>
    <a href="http://www.notepadplus.com.cn/notepad1.html">Notepad--</a>
</li>
<li>
    <a href="http://www.notepadplus.com.cn/notepad3.html">Notepad3</a>
</li><li>
    <a href="http://www.notepadplus.com.cn/vsc.html">Visual Studio Code</a>
</li>
</ul>
</li>

<li id="menu-item-2260" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-home current-menu-item page_item page-item-176 current_page_item menu-item-2260 megamenu-first-element menu-item-active"><a href="http://www.notepadplus.com.cn/">使用指南</a>
<ul>
<li>
	<a href="http://www.notepadplus.com.cn/zhinan/huanhang.html">notepad++怎么设置自动换行</a>
</li>
<li>
	<a href="http://www.notepadplus.com.cn/zhinan/quchu.html">notepad++怎么去除换行符</a>
</li><li>
	<a href="http://www.notepadplus.com.cn/zhinan/install.html">notepad++代码编辑器安装步骤</a>
</li>
<li>
	<a href="http://www.notepadplus.com.cn/zhinan/bidui.html">notepad++怎么比对两个文件</a>
</li>
<li>
	<a href="http://www.notepadplus.com.cn/zhinan/json.html">notepad++如何转换json</a>
</li>
<li>
	<a href="http://www.notepadplus.com.cn/zhinan/cn.html">notepad++怎么设置中文</a>
</li>
<li>
	<a href="http://www.notepadplus.com.cn/zhinan/zhengze.html">notepad++怎么正则匹配</a>
</li>
</ul>
</li>

</ul>
		</nav>


      <div id="mobile-menu" class="visible-xs">
        <div class="collapse navbar-collapse">
          <ul id="menu-main" class="nav navbar-nav">
            <li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-home current-menu-item page_item page-item-176 current_page_item menu-item-2260 menu-item-active active"><a title="Notepad" href="http://www.notepadplus.com.cn/">Notepad</a></li>
            <li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-home current-menu-item page_item page-item-176 current_page_item menu-item-2260 menu-item-active active"><a title="Notepad插件" href="http://www.notepadplus.com.cn/chajian.html">Notepad插件</a></li>
            <li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-home current-menu-item page_item page-item-176 current_page_item menu-item-2260 menu-item-active active"><a title="下载" href="http://www.notepadplus.com.cn/download.html">下载</a></li>
            <li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-home current-menu-item page_item page-item-176 current_page_item menu-item-2260 menu-item-active active"><a title="常见问题" href="http://www.notepadplus.com.cn/faq.html">常见问题</a></li>
            <li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-home current-menu-item page_item page-item-176 current_page_item menu-item-2260 menu-item-active active"><a title="关于" href="http://www.notepadplus.com.cn/about.html">关于</a></li>
            <li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-home current-menu-item page_item page-item-176 current_page_item menu-item-2260 menu-item-active active"><a title="notepad++的替代软件" href="http://www.notepadplus.com.cn/about.html">notepad++的替代软件</a>
              <ul>
                <li><a href="http://www.notepadplus.com.cn/notepad1.html">Notepad--</a></li>
                <li><a href="http://www.notepadplus.com.cn/notepad3.html">Notepad3</a></li>
                <li><a href="http://www.notepadplus.com.cn/vsc.html">Visual Studio Code</a></li>
              </ul>
            </li>
          </ul>
        </div>
      </div><!--/.visible-xs-->
    </div>
  </header><!--/#header-->
  <section id="main">
    <div class="container">
      <div class="row">
        <div class="col-lg-12">
          <div id="primary" class="content-area">

												<div id="content" class="site-content nonefixpos col-md-12" role="main">
																					<article id="post-176" class="post">
								<h1 class="entry-title singlepost">
									&#8203;
								</h1>

																<div class="entry-content">
<div class="vc_row wpb_row vc_row-fluid"><div class="wpb_column vc_column_container vc_col-sm-12"><div class="vc_column-inner">
    <div class="wpb_wrapper">
        <div class="main-content">
            <div class="content">
                <h1>Notepad3 简介</h1>
                <p>Notepad3是一款轻量级文本编辑器，是Windows记事本的优秀替代品。它基于Scintilla编辑器组件构建，由Florian Balmer的Notepad2和XhmikosR的Notepad2-mod发展而来，提供了丰富的功能和出色的性能。</p>

                <p>Notepad3拥有代码折叠、括号匹配、自动缩进、单词自动完成等功能，同时支持在ASCII、UTF-8和UTF-16等不同格式之间转换字符编码，以及DOS、Unix和Mac换行符格式的转换。</p>

                <div class="center">
                    <a href="https://rizonesoft.com/?sdm_process_download=1&download_id=17541" class="btn btn-primary btn-lg">
                        <i class="fa fa-download"></i> 32位 立即下载
                    </a>
                </div>
                <div class="center">
                    <a href="https://rizonesoft.com/?sdm_process_download=1&download_id=17525" class="btn btn-primary btn-lg">
                        <i class="fa fa-download"></i> 64位 立即下载
                    </a>
                </div>

                <h2>主要特点</h2>
                <ul class="feature-list">
                    <li>轻量级设计：启动快速，占用资源少</li>
                    <li>代码折叠：可折叠代码块，提高阅读效率</li>
                    <li>括号匹配：自动匹配括号，减少语法错误</li>
                    <li>自动缩进：智能缩进代码，保持格式一致</li>
                    <li>多种编程语言支持：提供丰富的语法高亮</li>
                    <li>多重撤销/重做：灵活编辑历史管理</li>
                    <li>书签功能：快速定位重要代码位置</li>
                    <li>正则表达式查找和替换：强大的文本处理能力</li>
                    <li>开源免费：基于开源许可协议</li>
                </ul>

                <h2>软件截图</h2>
                <h3>Notepad3主界面</h3>
                <img src="https://i0.wp.com/rizonesoft.com/wp-content/uploads/2023/09/notepad3-screenshot-1.jpg" alt="Notepad3主界面" class="screenshot">

                <h2>支持的编程语言</h2>
                <p>Notepad3支持多种编程语言的语法高亮，包括但不限于：Apache、ASP、Assembly、AutoHotkey、AutoIt3、AviSynth、Awk、Bash、BAT、C、C++、C#、CGI、CMake、CoffeeScript、CSS、CSV、D、Dart、DIFF、Fortran、Go、HTML、INF、INI、Inno Setup、Java、JavaScript、JSON、Julia、KiXtart、Kotlin、LaTeX、Lua、Makefiles、Markdown、MATLAB、Nim、NSIS、Pascal、Perl、PHP、PowerShell、Python、REG、Resource、R-S-SPlus Statistics、Ruby、Rust、Shell、SQL、Tcl、TOML、VB、VBScript、Verilog、VHDL、XHTML、XML、YAML等。</p>

                <h2>系统要求</h2>
                <div class="system-req">
                    <h3>Windows系统</h3>
                    <p>支持Windows 7/8/10/11</p>
                </div>

                <h2 style="text-align: center;">Notepad3 下载</h2>
                <div class="center">
                    <a href="https://rizonesoft.com/?sdm_process_download=1&download_id=17541" class="btn btn-primary btn-lg">
                        <i class="fa fa-download"></i> 32位 立即下载
                    </a>
                </div>
                <div class="center">
                    <a href="https://rizonesoft.com/?sdm_process_download=1&download_id=17525" class="btn btn-primary btn-lg">
                        <i class="fa fa-download"></i> 64位 立即下载
                    </a>
                </div>
            </div>

            <div class="sidebar">
                <div class="version-info">
                    <h3>最新版本</h3>
                    <p>当前最新版本：v6.24.1221.1</p>
                    <p>发布日期：2024年12月21日</p>
                    <div class="center">
                        <a href="https://rizonesoft.com/?sdm_process_download=1&download_id=17525" class="btn btn-primary btn-lg">
                            <i class="fa fa-download"></i> 下载最新版本
                        </a>
                    </div>
                </div>

                <h3>便携版下载</h3>
                <p>便携版可以直接从U盘或移动硬盘运行，不会在注册表中写入任何设置或在计算机上留下痕迹。</p>
                <div class="center">
                    <a href="https://rizonesoft.com/?sdm_process_download=1&download_id=17548" class="btn btn-primary btn-lg">
                        <i class="fa fa-download"></i> 便携版下载
                    </a>
                </div>

                <h3>安装说明</h3>
                <p>下载安装程序后运行即可开始安装过程。它支持使用干净、不显眼的基于注册表的方法替换默认的Windows记事本，也可以与Windows记事本一起安装。</p>

                <h3>更多功能</h3>
                <ul class="feature-list">
                    <li>字符编码转换：支持ASCII、UTF-8和UTF-16等</li>
                    <li>换行符格式转换：支持DOS、Unix和Mac格式</li>
                    <li>自定义语法高亮：可调整颜色和样式</li>
                    <li>文件比较：查看文件差异</li>
                    <li>代码导航：快速跳转到函数和类定义</li>
                </ul>

                <h3>源代码</h3>
                <p>Notepad3是开源软件，您可以在<a href="https://github.com/rizonesoft/Notepad3" target="_blank">GitHub</a>上查看和贡献源代码。</p>
            </div>
        </div>
    </div>

</div></div></div></div>


																	</div>
							</article>
													</div><!--/#content-->

				</div><!--/#primary-->
			</div><!--/.col-lg-12-->
		</div><!--/.row-->
	</div><!--/.container.-->
</section><!--/#main-->
<section id="bottom" class="wet-asphalt">
  <div class="container">
    <div class="row">
      <div class="col-sm-12 col-xs-12"><h3></h3>			<div class="textwidget"></div>
		</div>    </div>
  </div>
</section>
<footer id="footer" class="footer-bg" role="contentinfo">
  <div class="container">
    <div class="row">
	      <div class="col-sm-6">
        © 2020<a target="_blank" href="http://www.notepadplus.com.cn/" title="notepad">Notepad</a>本站非notepad开发者官方网站. All Rights Reserved.<script>
var _hmt = _hmt || [];
(function() {
  var hm = document.createElement("script");
  hm.src = "https://hm.baidu.com/hm.js?7224e9e4acdb6fa5155fcf6f10eee6b4";
  var s = document.getElementsByTagName("script")[0];
  s.parentNode.insertBefore(hm, s);
})();
</script>
		      </div>
	        <div class="col-sm-6">
        <ul class="pull-right">
          <li id="menu-item-250" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-home current-menu-item page_item page-item-176 current_page_item menu-item-250 menu-item-active"><a href="http://www.notepadplus.com.cn/">Notepad</a></li>
          <li>
            <a id="gototop" class="gototop" href="http://www.notepadplus.com.cn/#"><i class="fa fa-chevron-up"></i></a>          </li>
        </ul>
      </div>
	      </div>
  </div>
</footer><!--/#footer-->

      </div><!--/#boxed-->


	<script type="text/javascript">
		var c = document.body.className;
		c = c.replace(/woocommerce-no-js/, 'woocommerce-js');
		document.body.className = c;
	</script>
	<link rel="stylesheet" id="font-awesome-css" href="./index_files/font-awesome.min(1).css" type="text/css" media="all">
<script type="text/javascript">
/* <![CDATA[ */
var wpmenucart_ajax = {"ajaxurl":"http:\/\/www.notepadplus.com.cn\/wp-admin\/admin-ajax.php","nonce":"0f434f94e5"};
/* ]]> */
</script>
<script type="text/javascript" src="./index_files/wpmenucart.js"></script>
<script type="text/javascript">
/* <![CDATA[ */
var wpcf7 = {"apiSettings":{"root":"http:\/\/www.notepadplus.com.cn\/wp-json\/contact-form-7\/v1","namespace":"contact-form-7\/v1"}};
/* ]]> */
</script>
<script type="text/javascript" src="./index_files/scripts.js"></script>
<script type="text/javascript" src="./index_files/jquery.themepunch.tools.min.js" defer="defer"></script>
<script type="text/javascript" src="./index_files/jquery.themepunch.revolution.min.js" defer="defer"></script>
<script type="text/javascript" src="./index_files/js.cookie.min.js"></script>
<script type="text/javascript">
/* <![CDATA[ */
var woocommerce_params = {"ajax_url":"\/wp-admin\/admin-ajax.php","wc_ajax_url":"\/?wc-ajax=%%endpoint%%"};
/* ]]> */
</script>
<script type="text/javascript" src="./index_files/woocommerce.min.js"></script>
<script type="text/javascript">
/* <![CDATA[ */
var wc_cart_fragments_params = {"ajax_url":"\/wp-admin\/admin-ajax.php","wc_ajax_url":"\/?wc-ajax=%%endpoint%%","cart_hash_key":"wc_cart_hash_b0dd839f49d6aa5effbccdcbc9f318f4","fragment_name":"wc_fragments_b0dd839f49d6aa5effbccdcbc9f318f4"};
/* ]]> */
</script>
<script type="text/javascript" src="./index_files/cart-fragments.min.js"></script>
<script type="text/javascript" src="./index_files/jquery.background-video.js"></script>
<script type="text/javascript" src="./index_files/jquery.fancybox.min.js"></script>
<script type="text/javascript" src="./index_files/posts-block.js"></script>
<script type="text/javascript" src="./index_files/wooswipe.js"></script>
<script type="text/javascript" src="./index_files/jquery.dlmenu.js"></script>
<script type="text/javascript" src="./index_files/main.js"></script>
<script type="text/javascript">
/* <![CDATA[ */
var lvca_ajax_object = {"ajax_url":"http:\/\/www.notepadplus.com.cn\/wp-admin\/admin-ajax.php"};
/* ]]> */
</script>
<script type="text/javascript" src="./index_files/zhi-frontend.min.js"></script>
<script type="text/javascript" src="./index_files/wp-embed.min.js"></script>
<script type="text/javascript" src="./index_files/js_composer_front.min.js"></script>
<!-- BEGIN # MODAL LOGIN -->
<div class="modal fade" id="login-modal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true" style="display: none;">
    	<div class="modal-dialog">
			<div class="modal-content">
				<div class="modal-header" align="center">

					<button type="button" class="close" data-dismiss="modal" aria-label="Close">
						<i class="fa fa-remove" aria-hidden="true"></i>
					</button>
				</div>

                <!-- Begin # DIV Form -->
                <div id="div-forms">

                    <!-- Begin # Login Form -->
                    <form name="loginform" id="login-form" action="http://www.notepadplus.com.cn/wp-login.php" method="post">
		                <div class="modal-body">
				    		<div id="div-login-msg">
                                <div id="icon-login-msg" class="glyphicon glyphicon-chevron-right"></div>
                                <span id="text-login-msg">输入用户名和密码</span>
                            </div>
				    		<input id="username" name="log" class="form-control" type="text" placeholder="用户名" required="">
				    		<input id="password" name="pwd" class="form-control" type="password" placeholder="密码" required="">
                            <div class="checkbox">
                                <label>
                                    <input name="rememberme" id="rememberme" type="checkbox"> 记住我                                </label>
                            </div>
        		    	</div>
				        <div class="modal-footer">
                            <div>
                                                                        <input type="submit" name="user-submit" value="登录" class="btn btn-primary btn-lg btn-block">
                                        <input type="hidden" name="redirect_to" value="/">
                                        <input type="hidden" name="user-cookie" value="1">
                            </div>
				    	    <div>
                                <button id="login_lost_btn" type="button" class="btn btn-link">忘记密码？</button>
                                <button id="login_register_btn" type="button" class="btn btn-link">注册</button>
                            </div>
				        </div>
                    </form>
                    <!-- End # Login Form -->

                    <!-- Begin | Lost Password Form -->
                    <form id="lost-form" name="lostpasswordform" action="http://www.notepadplus.com.cn/wp-login.php?action=lostpassword" method="post" style="display:none;">
    	    		    <div class="modal-body">
		    				<div id="div-lost-msg">
                                <div id="icon-lost-msg" class="glyphicon glyphicon-chevron-right"></div>
                                <span id="text-lost-msg">输入电子邮箱。</span>
                            </div>
		    				<input id="lost_email" name="user_login" class="form-control" type="text" placeholder="电子邮箱" required="">
            			</div>
		    		    <div class="modal-footer">
						                            <div>
							<input type="submit" name="user-submit" value="发送" class="btn btn-primary btn-lg btn-block">

                            </div>
                            <div>
                                <button id="lost_login_btn" type="button" class="btn btn-link">登陆</button>
                                <button id="lost_register_btn" type="button" class="btn btn-link">注册</button>
                            </div>
		    		    </div>
                    </form>
                    <!-- End | Lost Password Form -->

                    <!-- Begin | Register Form -->
                    <form name="registerform" id="register-form" action="http://www.notepadplus.com.cn/wp-login.php?action=register" method="post" style="display:none;">
            		    <div class="modal-body">
		    				<div id="div-register-msg">
                                <div id="icon-register-msg" class="glyphicon glyphicon-chevron-right"></div>
                                <span id="text-register-msg">注册账户</span>
                            </div>
		    				<input id="register_username" name="user_login" class="form-control" type="text" placeholder="用户名" required="">
                            <input id="register_email" name="user_email" class="form-control" type="text" placeholder="电子邮箱" required="">
                            <input id="register_password" name="password" class="form-control" type="password" placeholder="密码" required="">
            			</div>
		    		    <div class="modal-footer">
                            <div>
														<input type="submit" name="user-submit" value="注册" class="btn btn-primary btn-lg btn-block">
														<input type="hidden" name="redirect_to" value="/?register=true">
							<input type="hidden" name="user-cookie" value="1">
                            </div>
                            <div>
                                <button id="register_login_btn" type="button" class="btn btn-link">登陆</button>
                                <button id="register_lost_btn" type="button" class="btn btn-link">忘记密码？</button>
                            </div>
		    		    </div>
                    </form>
                    <!-- End | Register Form -->

                </div>
                <!-- End # DIV Form -->

			</div>
		</div>
	</div>
    <!-- END # MODAL LOGIN -->
<script type="text/javascript">
jQuery(function($){$(window).scroll(function(){if($(window).scrollTop()>30){$('#header').addClass('affix');$("#wpadminbar").addClass("top-tool-column");$('#header').removeClass('transparent')}else{$('#header').removeClass('affix');$("#wpadminbar").removeClass("top-tool-column");$('#header').addClass('transparent')}})});
</script>
<script type="text/javascript">
jQuery(function($){$(".navsearch a").click(function(){$("#searchform1").fadeToggle();if($(this).find('i').hasClass('fa fa-search')){$(this).find('i').removeClass('fa fa-search');$(this).find('i').addClass('fa fa-remove')}else{$(this).find('i').removeClass('fa fa-remove');$(this).find('i').addClass('fa fa-search')}})});
</script>
<script type="text/javascript">
jQuery(function($){var $formLogin=$('#login-form');var $formLost=$('#lost-form');var $formRegister=$('#register-form');var $divForms=$('#div-forms');var $modalAnimateTime=300;var $msgAnimateTime=150;var $msgShowTime=2000;$('#login_register_btn').click(function(){modalAnimate($formLogin,$formRegister)});$('#register_login_btn').click(function(){modalAnimate($formRegister,$formLogin)});$('#login_lost_btn').click(function(){modalAnimate($formLogin,$formLost)});$('#lost_login_btn').click(function(){modalAnimate($formLost,$formLogin)});$('#lost_register_btn').click(function(){modalAnimate($formLost,$formRegister)});$('#register_lost_btn').click(function(){modalAnimate($formRegister,$formLost)});function modalAnimate($oldForm,$newForm){var $oldH=$oldForm.height();var $newH=$newForm.height();$divForms.css("height",$oldH);$oldForm.fadeToggle($modalAnimateTime,function(){$divForms.animate({height:$newH},$modalAnimateTime,function(){$newForm.fadeToggle($modalAnimateTime)})})}function msgFade($msgId,$msgText){$msgId.fadeOut($msgAnimateTime,function(){$(this).text($msgText).fadeIn($msgAnimateTime)})}function msgChange($divTag,$iconTag,$textTag,$divClass,$iconClass,$msgText){var $msgOld=$divTag.text();msgFade($textTag,$msgText);$divTag.addClass($divClass);$iconTag.removeClass("glyphicon-chevron-right");$iconTag.addClass($iconClass+" "+$divClass);setTimeout(function(){msgFade($textTag,$msgOld);$divTag.removeClass($divClass);$iconTag.addClass("glyphicon-chevron-right");$iconTag.removeClass($iconClass+" "+$divClass)},$msgShowTime)}});
</script>

<style type="text/css">
/* 确保内容始终显示 */
.vc_toggle_content {
    display: block !important;
}
/* 保留标题但移除点击功能 */
.vc_toggle_title {
    cursor: default !important;
}
/* 隐藏展开/折叠图标 */
.vc_toggle_icon {
    display: none !important;
}

/* Notepad-- 页面样式 */
.main-content {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -15px;
}

.content {
    flex: 1;
    min-width: 65%;
    padding: 0 15px;
}

.sidebar {
    width: 30%;
    padding: 0 15px;
    margin-top: 20px;
}

@media (max-width: 768px) {
    .content, .sidebar {
        width: 100%;
        min-width: 100%;
    }
}

.feature-list {
    padding-left: 20px;
}

.feature-list li {
    margin-bottom: 10px;
    line-height: 1.6;
}

.screenshot {
    max-width: 100%;
    height: auto;
    margin: 15px 0;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.system-req {
    background-color: #f9f9f9;
    border: 1px solid #eee;
    border-radius: 4px;
    padding: 15px;
    margin: 20px 0;
}

.version-info {
    background-color: #f5f5f5;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 20px;
}
</style>

</body></html>