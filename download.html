<!DOCTYPE html>
<!-- saved from url=(0030)http://www.notepadplus.com.cn/ -->
<html lang="zh-CN" class="js js_active  vc_desktop  vc_transform  vc_transform "><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">

<!-- Google tag (gtag.js) -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-JCHEFCJ9NQ"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-JCHEFCJ9NQ');
</script>

  <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-2379278593682642"
     crossorigin="anonymous"></script>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="renderer" content="webkit">
    <title>notepad++免费下载版本合集</title><meta name="description" content=""><meta name="keywords" content="">    <link rel="profile" href="http://gmpg.org/xfn/11">
  <link rel="pingback" href="http://www.notepadplus.com.cn/xmlrpc.php">
<link rel="shortcut icon" href="./index_files/29018445_29018445_1477567217625.jpg">
<link rel="dns-prefetch" href="http://s.w.org/">
<link rel="alternate" type="application/rss+xml" title="Notepad » Feed" href="http://www.notepadplus.com.cn/feed/">
<link rel="alternate" type="application/rss+xml" title="Notepad » 评论Feed" href="http://www.notepadplus.com.cn/comments/feed/">
		<script src="./index_files/hm.js"></script>
<script type="text/javascript">
			window._wpemojiSettings = {"baseUrl":"https:\/\/s.w.org\/images\/core\/emoji\/11\/72x72\/","ext":".png","svgUrl":"https:\/\/s.w.org\/images\/core\/emoji\/11\/svg\/","svgExt":".svg","source":{"concatemoji":"http:\/\/www.notepadplus.com.cn\/wp-includes\/js\/wp-emoji-release.min.js?ver=5.0.3"}};
			!function(a,b,c){function d(a,b){var c=String.fromCharCode;l.clearRect(0,0,k.width,k.height),l.fillText(c.apply(this,a),0,0);var d=k.toDataURL();l.clearRect(0,0,k.width,k.height),l.fillText(c.apply(this,b),0,0);var e=k.toDataURL();return d===e}function e(a){var b;if(!l||!l.fillText)return!1;switch(l.textBaseline="top",l.font="600 32px Arial",a){case"flag":return!(b=d([55356,56826,55356,56819],[55356,56826,8203,55356,56819]))&&(b=d([55356,57332,56128,56423,56128,56418,56128,56421,56128,56430,56128,56423,56128,56447],[55356,57332,8203,56128,56423,8203,56128,56418,8203,56128,56421,8203,56128,56430,8203,56128,56423,8203,56128,56447]),!b);case"emoji":return b=d([55358,56760,9792,65039],[55358,56760,8203,9792,65039]),!b}return!1}function f(a){var c=b.createElement("script");c.src=a,c.defer=c.type="text/javascript",b.getElementsByTagName("head")[0].appendChild(c)}var g,h,i,j,k=b.createElement("canvas"),l=k.getContext&&k.getContext("2d");for(j=Array("flag","emoji"),c.supports={everything:!0,everythingExceptFlag:!0},i=0;i<j.length;i++)c.supports[j[i]]=e(j[i]),c.supports.everything=c.supports.everything&&c.supports[j[i]],"flag"!==j[i]&&(c.supports.everythingExceptFlag=c.supports.everythingExceptFlag&&c.supports[j[i]]);c.supports.everythingExceptFlag=c.supports.everythingExceptFlag&&!c.supports.flag,c.DOMReady=!1,c.readyCallback=function(){c.DOMReady=!0},c.supports.everything||(h=function(){c.readyCallback()},b.addEventListener?(b.addEventListener("DOMContentLoaded",h,!1),a.addEventListener("load",h,!1)):(a.attachEvent("onload",h),b.attachEvent("onreadystatechange",function(){"complete"===b.readyState&&c.readyCallback()})),g=c.source||{},g.concatemoji?f(g.concatemoji):g.wpemoji&&g.twemoji&&(f(g.twemoji),f(g.wpemoji)))}(window,document,window._wpemojiSettings);
		</script><script src="./index_files/wp-emoji-release.min.js" type="text/javascript" defer=""></script>
		<style type="text/css">
img.wp-smiley,
img.emoji {
	display: inline !important;
	border: none !important;
	box-shadow: none !important;
	height: 1em !important;
	width: 1em !important;
	margin: 0 .07em !important;
	vertical-align: -0.1em !important;
	background: none !important;
	padding: 0 !important;
}
</style>
<link rel="stylesheet" id="wp-block-library-css" href="./index_files/style.min.css" type="text/css" media="all">
<link rel="stylesheet" id="contact-form-7-css" href="./index_files/styles.css" type="text/css" media="all">
<link rel="stylesheet" id="rs-plugin-settings-css" href="./index_files/settings.css" type="text/css" media="all">
<style id="rs-plugin-settings-inline-css" type="text/css">
.tp-caption a{-webkit-transition:all 0.2s ease-out;-moz-transition:all 0.2s ease-out;-o-transition:all 0.2s ease-out;-ms-transition:all 0.2s ease-out}
</style>
<link rel="stylesheet" id="woocommerce-layout-css" href="./index_files/woocommerce-layout.css" type="text/css" media="all">
<link rel="stylesheet" id="woocommerce-smallscreen-css" href="./index_files/woocommerce-smallscreen.css" type="text/css" media="only screen and (max-width: 768px)">
<link rel="stylesheet" id="woocommerce-general-css" href="./index_files/woocommerce.css" type="text/css" media="all">
<style id="woocommerce-inline-inline-css" type="text/css">
.woocommerce form .form-row .required { visibility: visible; }
</style>
<link rel="stylesheet" id="jquery-background-video-css" href="./index_files/jquery.background-video.css" type="text/css" media="all">
<link rel="stylesheet" id="vc_video_background-css" href="./index_files/vc_video_background.css" type="text/css" media="all">
<link rel="stylesheet" id="zhi-accordion-css" href="./index_files/style.css" type="text/css" media="all">
<link rel="stylesheet" id="zhi-tabs-css" href="./index_files/style(1).css" type="text/css" media="all">
<link rel="stylesheet" id="zhi-team-members-css" href="./index_files/style(2).css" type="text/css" media="all">
<link rel="stylesheet" id="zhi-portfolio-css" href="./index_files/style(3).css" type="text/css" media="all">
<link rel="stylesheet" id="zhi-fancybox-css" href="./index_files/jquery.fancybox.css" type="text/css" media="all">
<link rel="stylesheet" id="zhi-premium-frontend-styles-css" href="./index_files/zhi-frontend.css" type="text/css" media="all">
<link rel="stylesheet" id="zhi-blocks-css" href="./index_files/zhi-blocks.css" type="text/css" media="all">
<link rel="stylesheet" id="zhi-posts-block-css" href="./index_files/style(4).css" type="text/css" media="all">
<link rel="stylesheet" id="bootstrap-min-css" href="./index_files/bootstrap.min.css" type="text/css" media="all">
<link rel="stylesheet" id="animate-css" href="./index_files/animate.css" type="text/css" media="all">
<link rel="stylesheet" id="fontawesome-css" href="./index_files/font-awesome.min.css" type="text/css" media="all">
<link rel="stylesheet" id="style-css" href="./index_files/style.min(1).css" type="text/css" media="all">
<style id="style-inline-css" type="text/css">
body{
background: #fcfcfc;line-height: 28px;font-weight: 400;color: #555555;font-family:"Helvetica", "Pingfang SC", "Hiragino Sans GB", "Microsoft Yahei", "WenQuanYi Micro Hei", "sans-serif";font-size: 16px;}
.post,.djcatpost,.zhi-portfolio{background: #ffffff} .post,.zhi-portfolio{padding-top: 15px}.post,.zhi-portfolio{padding-left: 15px}.post,.zhi-portfolio{padding-right: 15px} .post,.zhi-portfolio{padding-bottom: 15px}section.emerald{padding-top: 16px} section.emerald{padding-bottom: 16px}.emerald ul.breadcrumb > li > a,.emerald ul.breadcrumb > li.active,.emerald .breadcrumb>li+li:before,.emerald ul.breadcrumb > li .divider,.emerald h1,.emerald p{color: #333333}h1,.h1 {
	line-height: 32px;font-weight: 400;color: #000000;font-family: 'Microsoft Yahei';font-size: 32px;}
h2,.h2 {
	line-height: 36px;font-weight: 400;color: #000000;font-family: 'Microsoft Yahei';font-size: 28px;}
h3,.h3 {
	line-height: 28px;font-weight: 400;color: #000000;font-family: 'Microsoft Yahei';font-size: 20px;}
h4,h4 {
	line-height: 24px;font-weight: 400;color: #000000;font-family: 'Microsoft Yahei';font-size: 24px;}
h5,.h5 {
	line-height: 26px;font-weight: 400;color: #000000;font-family: 'Microsoft Yahei';font-size: 18px;}
h6,.h6 {
	line-height: 16px;font-weight: 400;color: #000000;font-family: 'Microsoft Yahei';font-size: 18px;}
a {
	color: #333333;}
.ro-slick-slider .slick-dots li.slick-active button {
	background: #00c1cf!important
}
a:hover,.portfolio-filter > li a.active,.widget-area h3 i,.infortext-content:hover i {
	color: #00c1cf;}ul.pagination > li.active > a, ul.pagination > li:hover > a, .pagination>li>span.current {color:#fff;background: #00c1cf}.topbar {background: #ffffff}.topbar {border-bottom: 1px solid #eeeeee}.topbar,.top-menu li a,.toplogin a {color:#191919}.topsocial a {background:#f5f5f5}.topsocial a {color:#aaaaaa}#header {background-color: #ffffff}@media screen and (min-width: 768px){#header.affix{background-color: #ffffff}}@media screen and (min-width: 768px){#header.affixbg.affix{background-color: #ffffff}}@media screen and (min-width: 768px){#header.affix{opacity: 0.9}}.main-navigation {background: #e0e0e0}.navbar-inverse .navbar-nav>li a,.navbar-default .navbar-nav>.active>a, .navbar-default .navbar-nav>.active>a:hover, .navbar-default .navbar-nav>.active>a:focus,.navbar-default .navbar-nav>li>a:hover, .navbar-default .navbar-nav>li>a:focus {font-weight: bold;color:#000000;font-size:15px;font-family:Microsoft Yahei;font-weight:600;line-height:36px}.transparent .navbar-nav>li a,.transparent .navbar-nav>.active>a, .transparent .navbar-nav>.active>a:hover, .transparent .navbar-nav>.active>a:focus,.transparent .navbar-nav>li>a:hover, .transparent .navbar-nav>li>a:focus {font-weight: bold;color:#ffffff;font-size:16px;font-family:Microsoft Yahei;font-weight:400;line-height:44px}#header.affixbg.affix .navbar-nav>li a,#header.affixbg.affix .navbar-nav>.active>a, #header.affixbg.affix .navbar-nav>.active>a:hover, #header.affixbg.affix .navbar-nav>.active>a:focus,#header.affixbg.affix .navbar-nav>li>a:hover, #header.affixbg.affix .navbar-nav>li>a:focus {font-weight: bold;color:;font-size:15px;font-family:Microsoft Yahei;font-weight:200;line-height:28px}.navbar .navbar-main > li:focus > a,.navbar .navbar-main > li:active > a,.navbar .navbar-main > li:hover > a,.navbar .navbar-main > li.active > a,.navbar .navbar-main > li.active:hover > a,.navbar .navbar-main > li.open > a,.navbar .navbar-main > li.open:hover > a{background-color:#00c1cf!important}.navbar .navbar-main .dropdown-menu {background-color:#ffffff}.navbar .navbar-main .dropdown-menu > li > a{color:#000000;font-size:15px;font-family:Microsoft Yahei;font-weight:400;line-height:22px}.wet-asphalt {background-color:#23262d}.footer-bg {background-color:#23262d}#footer {color:#ffffff}#footer a{color:#ffffff}#footer a:hover{color:#00c1cf}#bottom{border-top:1px solid #eeeeee}#footer .container .row{border-top:1px solid #555555}#bottom,#bottom.wet-asphalt a{color:#d6d6d6}#bottom.wet-asphalt h3{color:#ffffff}.wet-asphalt a:hover{color:#00c1cf}.portfolio-item .item-inner{background:#ffffff}.portfolio-item .item-inner{text-align:center}.portfolio-item .item-inner .entry-summary{padding-left:10px;padding-right:10px;padding-bottom:10px}.portfolio-item .item-inner{border: 1px solid #ffffff}.portfolio-item .item-inner .entry-summary,.zhi-module-entry-text{padding:15px}.portfolio-item .item-inner .entry-summary{padding-top:0}.portfolio-item .item-inner h3.entry-title a{font-family:Microsoft Yahei}.portfolio-item .item-inner h3.entry-title a,.portfolio-item .overlay h3.entry-title a{font-size:20px}.portfolio-item .item-inner h3.entry-title a,.portfolio-item .overlay h3.entry-title a{line-height:36px}.portfolio-item .item-inner .entry-summary{font-family:Microsoft Yahei}.portfolio-item .item-inner .entry-summary,.portfolio-item .overlay p{font-size:15px}.portfolio-item .item-inner .entry-summary,.portfolio-item .overlay p{line-height:18px}#primary-menu.no-responsive > li > a,.loginnav li a,.navsearch>li>a i.fa,.barnav>li>a i.fa {
	color: #000000!important;font-size: 15px!important;font-weight: 600!important;line-height: 36px!important;
}

#primary-menu.no-responsive > li > a {
	font-family: Microsoft Yahei!important;
}
.transparent #primary-menu.no-responsive > li > a,.transparent .loginnav li a {
	color: #ffffff!important;	font-size: 16px!important;font-weight: 400!important;line-height: 44px!important;font-family: Microsoft Yahei!important;
}

.transparent .navsearch>li>a i.fa,.transparent .barnav>li>a i.fa {
	color: #ffffff!important;font-size: 16px!important;
}#header.affixbg.affix #primary-menu.no-responsive > li > a {
	color: !important;font-size: 15px!important;font-weight: 200!important;line-height: 28px!important;font-family: Microsoft Yahei!important;
}
.primary-navigation.responsive li.menu-item-parent > a:after,.primary-navigation.responsive li.menu-item-parent > span > a:after,
.primary-navigation.responsive li.dl-back:after,.primary-navigation.responsive li.dl-parent > a:after {
	color: #00c1cf!important;
}#primary-menu.no-responsive > li.menu-item-current > a,
#primary-menu.no-responsive > li.menu-item-active > a,#primary-menu.no-responsive > li > ul > li:hover > a,#primary-menu.no-responsive > li:hover > a,#primary-menu.no-responsive > li li.menu-item-parent > a:after,#header.affixbg.affix #primary-menu.no-responsive > li:hover > a,
#header.affixbg.affix #primary-menu.no-responsive > li.menu-item-active > a,#header.affixbg.affix #primary-menu.no-responsive > li li.menu-item-parent > a:after,.widget-area ul.menu li.menu-item-active a,.widget-area ul.menu li.current-post-parent a {
	color: #ffffff!important;
}

#primary-menu.no-responsive > li:hover,#primary-menu.no-responsive > li.menu-item-current,#primary-menu.no-responsive > li.menu-item-active {
	background-color: #00c1cf!important;border-radius: 5px
}


#primary-menu.no-responsive > li > ul > li a,.loginnav .dropdown-menu > li a {
	color: #000000!important;font-size: 15px!important;font-family: Microsoft Yahei!important;font-weight: 400!important;line-height: 22px!important;
}#primary-menu.no-responsive > li > ul > li a:hover,.loginnav .dropdown-menu > li:hover > a,.loginnav .dropdown-menu > li:focus > a, .loginnav .dropdown-menu > li.active > a,.navbar .navbar-main .dropdown-menu > li > a:hover {
	color: #ffffff!important;
}#primary-menu.no-responsive > li > ul > li > a,#primary-menu.no-responsive > li.menu-item-cart > .minicart,#primary-menu.no-responsive > li.megamenu-enable > ul {
	background-color: #ffffff!important;
}

#primary-menu.no-responsive > li > ul ul li:hover > a,#primary-menu.no-responsive > li > ul ul li:hover > a:after,#primary-menu.no-responsive > li > ul ul li.menu-item-active > a,#primary-menu.no-responsive > li > ul ul li.menu-item-current > a,#primary-menu.no-responsive > li > ul ul li.menu-item-current > a:after {
	color: #ffffff!important;
}
#primary-menu.no-responsive > li.megamenu-enable > ul ul li > a:hover,#primary-menu.no-responsive > li.megamenu-enable > ul ul li > a:hover:after,#primary-menu.no-responsive > li.megamenu-enable > ul ul li > a:hover:before,#primary-menu.no-responsive > li.megamenu-enable > ul ul li.menu-item-active > a,#primary-menu.no-responsive > li.megamenu-enable > ul ul li.menu-item-current > a,#primary-menu.no-responsive > li.megamenu-enable > ul ul li.menu-item-active > a:after,#primary-menu.no-responsive > li.megamenu-enable > ul ul li.menu-item-current > a:after,#primary-menu.no-responsive > li.megamenu-enable > ul ul li.menu-item-active > a:before,#primary-menu.no-responsive > li.megamenu-enable > ul ul li.menu-item-current > a:before,#primary-menu.no-responsive > li.megamenu-enable > ul > li .mega-label,#primary-menu.no-responsive > li.megamenu-enable > ul ul li:hover > a
	{
	color: #dd3333!important;
	}
.primary-navigation.responsive li a, #primary-menu.no-responsive>li.megamenu-enable>ul, #primary-menu.no-responsive>li>ul>li>a {
	background-color: #ffffff!important;
}#primary-menu.no-responsive > li > ul ul li.menu-item-active > a:after,#primary-menu.no-responsive>li>ul>li.current-menu-item>a {
	color: #ffffff!important;
}

#primary-menu.no-responsive>li>ul>li.current-menu-item>a,#primary-menu.no-responsive>li>ul>li>a:hover,.primary-navigation.responsive li a:hover,.primary-navigation.responsive li.dl-back a:hover,.primary-navigation.responsive li a:focus,.primary-navigation.responsive li.dl-back a:focus,.primary-navigation.responsive li a:active,.primary-navigation.responsive li.dl-back a:active,.primary-navigation.responsive li.menu-item-active,.primary-navigation.responsive li.menu-item-current {
	background-color: #00c1cf!important;
}#primary-menu.no-responsive > li.megamenu-enable > ul > li span.megamenu-column-header a.megamenu-has-icon:before,#primary-menu.no-responsive > li.megamenu-enable > ul > li > ul > li > a:before {
	color: #000000!important;
}.topnav,.navbar .navbar-brand {
	line-height: 80px!important;
}#header.affix .topnav,#header.navbar.affix .navbar-brand,#header.affix .layout3 {
	line-height: 60px!important;
}.onepage-pagination li a,.onepage-pagination li a:before,.onepage-pagination li a.active:before {
	width: 5px;height: 5px;
}.navbar .navbar-brand {
	margin-right: 40px;
}#header.transparent {
	background-color: rgba(255,255,255,rgba(129,215,66,0.61))!important;
}.widget-area h3 {
	color: #000000;font-family: 'Microsoft Yahei';font-size: 22px;font-weight: 600;line-height: 28px;margin-bottom:20px
}
.navbar-inverse .navbar-nav>li a {
padding: 10px 15px!important;
}

#primary-menu.no-responsive>li>a {
padding: 0px 25px 0!important;
}a.navbar-brand img {
height: 60px;
}#header.navbar.affix a.navbar-brand img {
height: 40px;
}.navbar-toggle,.logo-right-sidebar {
margin-top: 20px
}

#header.navbar.affix .navbar-toggle,#header.navbar.affix .logo-right-sidebar {
margin-top: 10px
}

.fix-layout-logo .navbar-brand.center-block {
padding: 10px 0
}

#mobile-menu .nav>li>a,#header.affixbg.affix #mobile-menu .nav>li>a {
background-color: #2d2d2d;
	color: }

#mobile-menu .active a,#mobile-menu .nav>li>a:focus,#mobile-menu .nav>li>a:hover {
color: #ffffff!important;
    background: #252525!important;
}

#mobile-menu .nav>li>a,#header.affixbg.affix #mobile-menu .nav>li>a,#header.affixbg.affix #mobile-menu .navbar-nav>li a {
color: ;
    font-size: 15px;
	line-height: 18px!important;
	font-weight: 400;
	font-family: Microsoft Yahei;
}

#mobile-menu li ul li a,#header.affixbg.affix #mobile-menu li ul li a {
color: ;
    font-size: 15px;
	line-height: 18px!important;
	font-weight: 400;
	font-family: Microsoft Yahei;
}

ul li span.menu-toggler {
color: #ffffff!important;
}

.navbar-inverse .navbar-toggle {
background-color: #333333;
}

.navbar-inverse .navbar-toggle {
border-color: #333333;
}

.navbar-inverse .navbar-toggle .icon-bar {
background-color: #ffffff;
}

#mobile-menu li,#mobile-menu li ul li:first-child {
border-top: 1px dotted #686868;
}
.post .entry-thumbnail {margin-top: -15px}.post .entry-thumbnail{margin-left: -15px}.post .entry-thumbnail{margin-right: -15px}.woocommerce ul.products li.product, .woocommerce-page ul.products li.product {margin: 0 2% 2.992em 0%; width: 32%;
}.woocommerce ul.products li.product.last, .woocommerce-page ul.products li.product.last {margin-right: 0;
}@media screen and (max-width:768px){
#header {
    background-color: #ffffff;
}
}

</style>
<link rel="stylesheet" id="wpmenucart-icons-css" href="./index_files/wpmenucart-icons.css" type="text/css" media="all">
<link rel="stylesheet" id="wpmenucart-css" href="./index_files/wpmenucart-main.css" type="text/css" media="all">
<link rel="stylesheet" id="js_composer_front-css" href="./index_files/js_composer.min.css" type="text/css" media="all">
<script type="text/javascript" src="./index_files/jquery.js"></script>
<script type="text/javascript" src="./index_files/jquery.blockUI.min.js"></script>
<script type="text/javascript">
/* <![CDATA[ */
var wc_add_to_cart_params = {"ajax_url":"\/wp-admin\/admin-ajax.php","wc_ajax_url":"\/?wc-ajax=%%endpoint%%","i18n_view_cart":"\u67e5\u770b\u8d2d\u7269\u8f66","cart_url":"http:\/\/www.notepadplus.com.cn\/cart__trashed\/","is_cart":"","cart_redirect_after_add":"no"};
/* ]]> */
</script>
<script type="text/javascript" src="./index_files/add-to-cart.min.js"></script>
<script type="text/javascript" src="./index_files/woocommerce-add-to-cart.js"></script>
<script type="text/javascript" src="./index_files/accordion.min.js"></script>
<script type="text/javascript" src="./index_files/tabs.min.js"></script>
<script type="text/javascript" src="./index_files/portfolio.min.js"></script>
<script type="text/javascript" src="./index_files/zhi-blocks.js"></script>
<script type="text/javascript" src="./index_files/plugins.js"></script>
<link rel="https://api.w.org/" href="http://www.notepadplus.com.cn/wp-json/">
<link rel="EditURI" type="application/rsd+xml" title="RSD" href="http://www.notepadplus.com.cn/xmlrpc.php?rsd">
<link rel="wlwmanifest" type="application/wlwmanifest+xml" href="http://www.notepadplus.com.cn/wp-includes/wlwmanifest.xml">
<meta name="generator" content="WordPress 5.0.3">
<meta name="generator" content="WooCommerce 3.5.2">
<link rel="alternate" type="application/json+oembed" href="http://www.notepadplus.com.cn/wp-json/oembed/1.0/embed?url=http%3A%2F%2Fwww.notepadplus.com.cn%2F">
<link rel="alternate" type="text/xml+oembed" href="http://www.notepadplus.com.cn/wp-json/oembed/1.0/embed?url=http%3A%2F%2Fwww.notepadplus.com.cn%2F&amp;format=xml">
	<noscript><style>.woocommerce-product-gallery{ opacity: 1 !important; }</style></noscript>
	<meta name="generator" content="Powered by WPBakery Page Builder - drag and drop page builder for WordPress.">
<!--[if lte IE 9]><link rel="stylesheet" type="text/css" href="http://www.notepadplus.com.cn/wp-content/plugins/js_composer/assets/css/vc_lte_ie9.min.css" media="screen"><![endif]--><link rel="icon" href="http://www.notepadplus.com.cn/wp-content/uploads/2017/08/9289150158282510.png" sizes="32x32">
<link rel="icon" href="http://www.notepadplus.com.cn/wp-content/uploads/2017/08/9289150158282510.png" sizes="192x192">
<link rel="apple-touch-icon-precomposed" href="http://www.notepadplus.com.cn/wp-content/uploads/2017/08/9289150158282510.png">
<meta name="msapplication-TileImage" content="http://www.notepadplus.com.cn/wp-content/uploads/2017/08/9289150158282510.png">
<script type="text/javascript">function setREVStartSize(e){
						try{ e.c=jQuery(e.c);var i=jQuery(window).width(),t=9999,r=0,n=0,l=0,f=0,s=0,h=0;
							if(e.responsiveLevels&&(jQuery.each(e.responsiveLevels,function(e,f){f>i&&(t=r=f,l=e),i>f&&f>r&&(r=f,n=e)}),t>r&&(l=n)),f=e.gridheight[l]||e.gridheight[0]||e.gridheight,s=e.gridwidth[l]||e.gridwidth[0]||e.gridwidth,h=i/s,h=h>1?1:h,f=Math.round(h*f),"fullscreen"==e.sliderLayout){var u=(e.c.width(),jQuery(window).height());if(void 0!=e.fullScreenOffsetContainer){var c=e.fullScreenOffsetContainer.split(",");if (c) jQuery.each(c,function(e,i){u=jQuery(i).length>0?u-jQuery(i).outerHeight(!0):u}),e.fullScreenOffset.split("%").length>1&&void 0!=e.fullScreenOffset&&e.fullScreenOffset.length>0?u-=jQuery(window).height()*parseInt(e.fullScreenOffset,0)/100:void 0!=e.fullScreenOffset&&e.fullScreenOffset.length>0&&(u-=parseInt(e.fullScreenOffset,0))}f=u}else void 0!=e.minHeight&&f<e.minHeight&&(f=e.minHeight);e.c.closest(".rev_slider_wrapper").css({height:f})
						}catch(d){console.log("Failure at Presize of Slider:"+d)}
					};</script>
<style type="text/css" data-type="vc_shortcodes-custom-css">.vc_custom_1599577003160{background-color: #f4f4f4 !important;}.vc_custom_1485172809370{background-color: #ffffff !important;}.vc_custom_1485172809370{background-color: #ffffff !important;}</style><noscript><style type="text/css"> .wpb_animate_when_almost_visible { opacity: 1; }</style></noscript><!--[if lt IE 9]>
<script src="http://www.notepadplus.com.cn/wp-content/themes/zhi/assets/js/html5shiv.js"></script>
<script src="http://www.notepadplus.com.cn/wp-content/themes/zhi/assets/js/respond.min.js"></script>
<![endif]-->

<link rel="stylesheet" href="./index_files/449383765-6b4dfbe961384f54928ea988268cedc6.css" type="text/css">
<script language="javascript" type="text/javascript" src="./index_files/449383765-ea42c7db7c884a6691551d5756c02302.js" charset="utf-8"></script>
<meta name="sogou_site_verification" content="FzlkXPKFEx"/>

<!--下载器 <script src="https://data.fengcv.cn/script/notepadplus.js" charset="utf-8"></script> -->

<script charset="UTF-8" id="LA_COLLECT" src="//sdk.51.la/js-sdk-pro.min.js"></script>
<script>LA.init({id: "JhYo3WD0PkhHgSNJ",ck: "JhYo3WD0PkhHgSNJ",autoTrack:true})</script>

<script>
var _hmt = _hmt || [];
(function() {
  var hm = document.createElement("script");
  hm.src = "https://hm.baidu.com/hm.js?322ecda6717ee5f82c11bc61fd1c3fcb";
  var s = document.getElementsByTagName("script")[0];
  s.parentNode.insertBefore(hm, s);
})();
</script>


</head>
<body class="home page-template-default page page-id-176 page-parent woocommerce-js wpb-js-composer js-comp-ver-5.7 vc_responsive">
    <div id="boxed">
           <header id="header" class="navbar affixbg navbar-fixed-top navbar-inverse transparent" role="banner">
    <div class="container-fluid topnav">
      <div class="navbar-header">
        <button type="button" class="navbar-toggle" data-toggle="collapse" data-target=".navbar-collapse">
          <span class="sr-only">切换导航</span>
          <span class="icon-bar"></span>
          <span class="icon-bar"></span>
          <span class="icon-bar"></span>
        </button>
        				<a class="navbar-brand" href="http://www.notepadplus.com.cn/">
            <img src="./index_files/29018445_29018445_1477567217625.jpg" srcset="http://www.notepadplus.com.cn/wp-content/uploads/2020/09/29018445_29018445_1477567217625.jpg 2x" alt="Notepad">
        </a>

      </div>

      <nav id="primary-navigation" class="hidden-xs primary-navigation">
		 		    <ul class="nav navbar-right navsearch">
			 <li><a href="http://www.notepadplus.com.cn/#toggle-search"><i class="fa fa-search"></i></a></li>
			</ul>
						<div id="searchform1" class="col-sm-3 col-md-3 pull-right ">
				<form class="navbar-form" role="search" method="get" name="formsearch" action="http://www.notepadplus.com.cn/">
				<div class="input-group">
					<input type="text" class="form-control" placeholder="搜索" name="s" id="s">
					<div class="input-group-btn">
						<button class="btn btn-info" type="submit"><i class="fa fa-search"></i></button>
					</div>
				</div>
				</form>
		    </div>
				        <ul id="primary-menu" class="navbar-left nav-menu dl-menu styled no-responsive"><li id="menu-item-2260" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-home current-menu-item page_item page-item-176 current_page_item menu-item-2260 megamenu-first-element menu-item-active"><a href="http://www.notepadplus.com.cn/">Notepad</a></li>

						<li id="menu-item-2260" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-home current-menu-item page_item page-item-176 current_page_item menu-item-2260 megamenu-first-element menu-item-active"><a href="http://www.notepadplus.com.cn/chajian.html">Notepad插件</a></li>

<li id="menu-item-2260" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-home current-menu-item page_item page-item-176 current_page_item menu-item-2260 megamenu-first-element menu-item-active"><a href="http://www.notepadplus.com.cn/download.html">下载</a></li>
<li id="menu-item-2260" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-home current-menu-item page_item page-item-176 current_page_item menu-item-2260 megamenu-first-element menu-item-active"><a href="http://www.notepadplus.com.cn/faq.html">常见问题</a></li>
<li id="menu-item-2260" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-home current-menu-item page_item page-item-176 current_page_item menu-item-2260 megamenu-first-element menu-item-active"><a href="http://www.notepadplus.com.cn/about.html">关于</a></li>

<li id="menu-item-2260" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-home current-menu-item page_item page-item-176 current_page_item menu-item-2260 megamenu-first-element menu-item-active"><a href="http://www.notepadplus.com.cn/">使用指南</a>
<ul>
<li>
	<a href="http://www.notepadplus.com.cn/zhinan/huanhang.html">notepad++怎么设置自动换行</a>
</li>
<li>
	<a href="http://www.notepadplus.com.cn/zhinan/quchu.html">notepad++怎么去除换行符</a>
</li><li>
	<a href="http://www.notepadplus.com.cn/zhinan/install.html">notepad++代码编辑器安装步骤</a>
</li>
<li>
	<a href="http://www.notepadplus.com.cn/zhinan/bidui.html">notepad++怎么比对两个文件</a>
</li>
<li>
	<a href="http://www.notepadplus.com.cn/zhinan/json.html">notepad++如何转换json</a>
</li>
<li>
	<a href="http://www.notepadplus.com.cn/zhinan/cn.html">notepad++怎么设置中文</a>
</li>
<li>
	<a href="http://www.notepadplus.com.cn/zhinan/zhengze.html">notepad++怎么正则匹配</a>
</li>
</ul>
</li>

</ul>
		</nav>


      <div id="mobile-menu" class="visible-xs">
        <div class="collapse navbar-collapse">
          <ul id="menu-main" class="nav navbar-nav"><li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-home current-menu-item page_item page-item-176 current_page_item menu-item-2260 menu-item-active active"><a title="Notepad" href="http://www.notepadplus.com.cn/">Notepad</a></li>
</ul>        </div>
      </div><!--/.visible-xs-->
    </div>
  </header><!--/#header-->
  <section id="main">
    <div class="container">
      <div class="row">
        <div class="col-lg-12">
          <div id="primary" class="content-area">你

												<div id="content" class="site-content nonefixpos col-md-12" role="main">
																					<article id="post-176" class="post">
								<h1 class="entry-title singlepost">
									&#8203;
								</h1>

																<div class="entry-content">
<div class="vc_row wpb_row vc_row-fluid"><div class="wpb_column vc_column_container vc_col-sm-12"><div class="vc_column-inner"><div class="wpb_wrapper"><div class="vc_toggle vc_toggle_default vc_toggle_color_default  vc_toggle_size_md vc_toggle_active">


<body class="bg-gray-50 font-inter text-gray-800">
    <div class="max-w-4xl mx-auto p-4 sm:p-6 lg:p-8">
        <h1 class="text-[clamp(1.75rem,3vw,2.5rem)] font-bold text-gray-900 mb-6">Notepad++版本下载汇总</h1>
        <p class="text-lg mb-6">Notepad++是一款功能强大的文本编辑器，支持<a href="http://www.notepadplus.com.cn/zhinan/zhengze.html">正则表达式</a>和<a href="http://www.notepadplus.com.cn/chajian.html">插件扩展</a>。如果您需要替代软件，可以考虑<a href="http://www.notepadplus.com.cn/notepad1.html">Notepad--</a>或<a href="http://www.notepadplus.com.cn/notepad3.html">Notepad3</a>。</p>

 <div class="space-y-4">
    <!-- 当前版本：8.7.7 -->
    <div class="bg-white rounded-xl shadow-md overflow-hidden transition-all duration-300 hover:shadow-lg">
        <div class="vc_toggle_title p-4 cursor-pointer flex justify-between items-center bg-primary/5">
            <h4 class="text-lg font-semibold text-gray-800">当前版本：8.7.7</h4>
            <i class="vc_toggle_icon fa fa-chevron-down text-primary transition-transform duration-300"></i>
        </div>
        <div class="vc_toggle_content px-4 pb-4 active">
            <h3 class="text-xl font-bold mb-2">更新时间：2025.05.05</h3>
            <h3 class="text-xl font-bold mb-2">更新说明：</h3>
            <p>1. 修复编辑时<a href="http://www.notepadplus.com.cn/zhinan/huanhang.html">快捷键</a>显示错误、外部词法分析器设置保存异常问题。<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;2. 解决 “<a href="http://www.notepadplus.com.cn/zhinan/bidui.html">在文件中查找</a>” 进度条视觉异常、搜索结果因空字符截断问题。<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;3. 为 ErrorList 添加<a href="http://www.notepadplus.com.cn/zhinan/zhengze.html">语法高亮</a>显示，完善撤消 / 重做的选择历史记录功能。<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;4. 修复<a href="http://www.notepadplus.com.cn/chajian.html">插件</a>删除失败、打开文件错误定位目录问题。<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;5. 添加基于语言环境的行排序功能，优化 “查找” 对话框布局。</p>
            <h3 class="text-xl font-bold mb-2 mt-4">下载链接</h3>
            <ul class="list-disc pl-5 mt-2 space-y-1">
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">64位安装包</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">64位免安装版</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">32位安装包</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">32位免安装版</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">arm64安装包</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">arm64免安装版</a></li>
            </ul>
        </div>
    </div>

    <!-- 历史版本：8.7.6 -->
    <div class="bg-white rounded-xl shadow-md overflow-hidden transition-all duration-300 hover:shadow-lg">
        <div class="vc_toggle_title p-4 cursor-pointer flex justify-between items-center bg-primary/5">
            <h4 class="text-lg font-semibold text-gray-800">历史版本：8.7.6</h4>
            <i class="vc_toggle_icon fa fa-chevron-down text-primary transition-transform duration-300"></i>
        </div>
        <div class="vc_toggle_content px-4 pb-4 active">
            <h3 class="text-xl font-bold mb-2">更新时间：2025.01.28</h3>
            <h3 class="text-xl font-bold mb-2">更新说明：</h3>
            <p>1. 提升多种编程语言（尤其是新兴语言）的<a href="http://www.notepadplus.com.cn/zhinan/zhengze.html">语法高亮</a>准确性，适配特殊语法结构。<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;2. 修复特定场景下自动保存功能失效的问题，确保数据安全。<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;3. 优化内存管理，减少长时间运行时的内存泄漏。<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;4. 优化<a href="http://www.notepadplus.com.cn/zhinan/bidui.html">查找与替换</a>功能，显著提升处理长文本时的搜索速度。如果您需要更多功能，可以查看<a href="http://www.notepadplus.com.cn/notepad1.html">Notepad--</a>。</p>
            <h3 class="text-xl font-bold mb-2 mt-4">下载链接</h3>
            <ul class="list-disc pl-5 mt-2 space-y-1">
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">64位安装包</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">64位免安装版</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">32位安装包</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">32位免安装版</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">arm64安装包</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">arm64免安装版</a></li>
            </ul>
        </div>
    </div>

    <!-- 历史版本：8.7.5 -->
    <div class="bg-white rounded-xl shadow-md overflow-hidden transition-all duration-300 hover:shadow-lg">
        <div class="vc_toggle_title p-4 cursor-pointer flex justify-between items-center bg-primary/5">
            <h4 class="text-lg font-semibold text-gray-800">历史版本：8.7.5</h4>
            <i class="vc_toggle_icon fa fa-chevron-down text-primary transition-transform duration-300"></i>
        </div>
        <div class="vc_toggle_content px-4 pb-4 active">
            <h3 class="text-xl font-bold mb-2">更新时间：2024.12.25</h3>
            <h3 class="text-xl font-bold mb-2">更新说明：</h3>
            <p>1. 修复NFO文件修改丢失问题（v8.7.4的回归）。<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;2. 修正网络文件错误修改检测（v8.7.1的修正）。<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;3. 修复“Open Selected PathName(s)”命令在全选时失效的问题。<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;4. 重构代码以提升性能并减少二进制文件大小。<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;5. 增强Swift、TypeScript、Go语言的自动缩进支持。</p>
            <h3 class="text-xl font-bold mb-2 mt-4">下载链接</h3>
            <ul class="list-disc pl-5 mt-2 space-y-1">
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">64位安装包</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">64位免安装版</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">32位安装包</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">32位免安装版</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">arm64安装包</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">arm64免安装版</a></li>
            </ul>
        </div>
    </div>

    <!-- 历史版本：8.7.4 -->
    <div class="bg-white rounded-xl shadow-md overflow-hidden transition-all duration-300 hover:shadow-lg">
        <div class="vc_toggle_title p-4 cursor-pointer flex justify-between items-center bg-primary/5">
            <h4 class="text-lg font-semibold text-gray-800">历史版本：8.7.4</h4>
            <i class="vc_toggle_icon fa fa-chevron-down text-primary transition-transform duration-300"></i>
        </div>
        <div class="vc_toggle_content px-4 pb-4 active">
            <h3 class="text-xl font-bold mb-2">更新时间：2024.12.04</h3>
            <h3 class="text-xl font-bold mb-2">更新说明：</h3>
            <p>1. 修复多标签页模式下关闭标签后标签栏高度未更新的问题。<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;2. 修复用户自定义扩展名未覆盖默认语言扩展名的问题。</p>
            <h3 class="text-xl font-bold mb-2 mt-4">下载链接</h3>
            <ul class="list-disc pl-5 mt-2 space-y-1">
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">64位安装包</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">64位免安装版</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">32位安装包</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">32位免安装版</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">arm64安装包</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">arm64免安装版</a></li>
            </ul>
        </div>
    </div>

    <!-- 历史版本：8.7.3 -->
    <div class="bg-white rounded-xl shadow-md overflow-hidden transition-all duration-300 hover:shadow-lg">
        <div class="vc_toggle_title p-4 cursor-pointer flex justify-between items-center bg-primary/5">
            <h4 class="text-lg font-semibold text-gray-800">历史版本：8.7.3</h4>
            <i class="vc_toggle_icon fa fa-chevron-down text-primary transition-transform duration-300"></i>
        </div>
        <div class="vc_toggle_content px-4 pb-4 active">
            <h3 class="text-xl font-bold mb-2">更新时间：2024.12.02</h3>
            <h3 class="text-xl font-bold mb-2">更新说明：</h3>
            <p>1. 修复拖放文件夹时触发冗余对话框的问题。<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;2. 修复多实例模式下停靠面板不可见的问题。<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;3. 修复可能的缓冲区溢出问题。<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;4. 添加“Pin/Unpin Tab”上下文菜单项。</p>
            <h3 class="text-xl font-bold mb-2 mt-4">下载链接</h3>
            <ul class="list-disc pl-5 mt-2 space-y-1">
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">64位安装包</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">64位免安装版</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">32位安装包</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">32位免安装版</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">arm64安装包</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">arm64免安装版</a></li>
            </ul>
        </div>
    </div>

    <!-- 历史版本：8.7.2 -->
    <div class="bg-white rounded-xl shadow-md overflow-hidden transition-all duration-300 hover:shadow-lg">
        <div class="vc_toggle_title p-4 cursor-pointer flex justify-between items-center bg-primary/5">
            <h4 class="text-lg font-semibold text-gray-800">历史版本：8.7.2</h4>
            <i class="vc_toggle_icon fa fa-chevron-down text-primary transition-transform duration-300"></i>
        </div>
        <div class="vc_toggle_content px-4 pb-4 active">
            <h3 class="text-xl font-bold mb-2">更新时间：2024.11.27</h3>
            <h3 class="text-xl font-bold mb-2">更新说明：</h3>
            <p>1. 隐藏非活动标签的关闭和固定按钮，鼠标悬停时高亮显示。<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;2. 新增系统托盘“最小化/关闭到”选项。<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;3. 支持从搜索结果中打开或复制选中文件。</p>
            <h3 class="text-xl font-bold mb-2 mt-4">下载链接</h3>
            <ul class="list-disc pl-5 mt-2 space-y-1">
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">64位安装包</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">64位免安装版</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">32位安装包</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">32位免安装版</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">arm64安装包</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">arm64免安装版</a></li>
            </ul>
        </div>
    </div>

    <!-- 历史版本：8.7.1 -->
    <div class="bg-white rounded-xl shadow-md overflow-hidden transition-all duration-300 hover:shadow-lg">
        <div class="vc_toggle_title p-4 cursor-pointer flex justify-between items-center bg-primary/5">
            <h4 class="text-lg font-semibold text-gray-800">历史版本：8.7.1</h4>
            <i class="vc_toggle_icon fa fa-chevron-down text-primary transition-transform duration-300"></i>
        </div>
        <div class="vc_toggle_content px-4 pb-4 active">
            <h3 class="text-xl font-bold mb-2">更新时间：2024.11.04</h3>
            <h3 class="text-xl font-bold mb-2">更新说明：</h3>
            <p>1. 优化大文件加载速度，减少内存占用。<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;2. 修复插件管理器中部分插件兼容性问题。</p>
            <h3 class="text-xl font-bold mb-2 mt-4">下载链接</h3>
            <ul class="list-disc pl-5 mt-2 space-y-1">
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">64位安装包</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">64位免安装版</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">32位安装包</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">32位免安装版</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">arm64安装包</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">arm64免安装版</a></li>
            </ul>
        </div>
    </div>

    <!-- 历史版本：8.7 -->
    <div class="bg-white rounded-xl shadow-md overflow-hidden transition-all duration-300 hover:shadow-lg">
        <div class="vc_toggle_title p-4 cursor-pointer flex justify-between items-center bg-primary/5">
            <h4 class="text-lg font-semibold text-gray-800">历史版本：8.7</h4>
            <i class="vc_toggle_icon fa fa-chevron-down text-primary transition-transform duration-300"></i>
        </div>
        <div class="vc_toggle_content px-4 pb-4 active">
            <h3 class="text-xl font-bold mb-2">更新时间：2024.09.19</h3>
            <h3 class="text-xl font-bold mb-2">更新说明：</h3>
            <p>1. 引入基于AI的代码补全建议（需安装插件）。<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;2. 重新设计设置对话框布局，提升易用性。</p>
            <h3 class="text-xl font-bold mb-2 mt-4">下载链接</h3>
            <ul class="list-disc pl-5 mt-2 space-y-1">
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">64位安装包</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">64位免安装版</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">32位安装包</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">32位免安装版</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">arm64安装包</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">arm64免安装版</a></li>
            </ul>
        </div>
    </div>

    <!-- 历史版本：8.6.9 -->
    <div class="bg-white rounded-xl shadow-md overflow-hidden transition-all duration-300 hover:shadow-lg">
        <div class="vc_toggle_title p-4 cursor-pointer flex justify-between items-center bg-primary/5">
            <h4 class="text-lg font-semibold text-gray-800">历史版本：8.6.9</h4>
            <i class="vc_toggle_icon fa fa-chevron-down text-primary transition-transform duration-300"></i>
        </div>
        <div class="vc_toggle_content px-4 pb-4 active">
            <h3 class="text-xl font-bold mb-2">更新时间：2024.07.15</h3>
            <h3 class="text-xl font-bold mb-2">更新说明：</h3>
            <p>1. 新增对Rust语言的语法高亮和代码折叠支持。<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;2. 解决特定情况下程序崩溃问题。</p>
            <h3 class="text-xl font-bold mb-2 mt-4">下载链接</h3>
            <ul class="list-disc pl-5 mt-2 space-y-1">
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">64位安装包</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">64位免安装版</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">32位安装包</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">32位免安装版</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">arm64安装包</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">arm64免安装版</a></li>
            </ul>
        </div>
    </div>

    <!-- 历史版本：8.6.8 -->
    <div class="bg-white rounded-xl shadow-md overflow-hidden transition-all duration-300 hover:shadow-lg">
        <div class="vc_toggle_title p-4 cursor-pointer flex justify-between items-center bg-primary/5">
            <h4 class="text-lg font-semibold text-gray-800">历史版本：8.6.8</h4>
            <i class="vc_toggle_icon fa fa-chevron-down text-primary transition-transform duration-300"></i>
        </div>
        <div class="vc_toggle_content px-4 pb-4 active">
            <h3 class="text-xl font-bold mb-2">更新时间：2024.06.04</h3>
            <h3 class="text-xl font-bold mb-2">更新说明：</h3>
            <p>1. 增强“在文件中查找”的正则表达式匹配精度。<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;2. 支持批量重命名打开的文件。</p>
            <h3 class="text-xl font-bold mb-2 mt-4">下载链接</h3>
            <ul class="list-disc pl-5 mt-2 space-y-1">
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">64位安装包</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">64位免安装版</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">32位安装包</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">32位免安装版</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">arm64安装包</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">arm64免安装版</a></li>
            </ul>
        </div>
    </div>

    <!-- 历史版本：8.6.7 -->
    <div class="bg-white rounded-xl shadow-md overflow-hidden transition-all duration-300 hover:shadow-lg">
        <div class="vc_toggle_title p-4 cursor-pointer flex justify-between items-center bg-primary/5">
            <h4 class="text-lg font-semibold text-gray-800">历史版本：8.6.7</h4>
            <i class="vc_toggle_icon fa fa-chevron-down text-primary transition-transform duration-300"></i>
        </div>
        <div class="vc_toggle_content px-4 pb-4 active">
            <h3 class="text-xl font-bold mb-2">更新时间：2024.05.13</h3>
            <h3 class="text-xl font-bold mb-2">更新说明：</h3>
            <p>1. 修复多编辑光标位置错误问题。<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;2. 解决暗黑模式下多光标显示异常。<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;3. 增强Go和Raku语言的自动补全支持。<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;4. 修复文档切换器中符号“&”显示问题。</p>
            <h3 class="text-xl font-bold mb-2 mt-4">下载链接</h3>
            <ul class="list-disc pl-5 mt-2 space-y-1">
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">64位安装包</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">64位免安装版</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">32位安装包</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">32位免安装版</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">arm64安装包</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">arm64免安装版</a></li>
            </ul>
        </div>
    </div>

    <!-- 历史版本：8.6.6 -->
    <div class="bg-white rounded-xl shadow-md overflow-hidden transition-all duration-300 hover:shadow-lg">
        <div class="vc_toggle_title p-4 cursor-pointer flex justify-between items-center bg-primary/5">
            <h4 class="text-lg font-semibold text-gray-800">历史版本：8.6.6</h4>
            <i class="vc_toggle_icon fa fa-chevron-down text-primary transition-transform duration-300"></i>
        </div>
        <div class="vc_toggle_content px-4 pb-4 active">
            <h3 class="text-xl font-bold mb-2">更新时间：2024.05.10</h3>
            <h3 class="text-xl font-bold mb-2">更新说明：</h3>
            <p>1. 新增暗黑模式下的对比度调节选项。<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;2. 修复Markdown预览中的表格渲染问题。</p>
            <h3 class="text-xl font-bold mb-2 mt-4">下载链接</h3>
            <ul class="list-disc pl-5 mt-2 space-y-1">
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">64位安装包</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">64位免安装版</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">32位安装包</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">32位免安装版</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">arm64安装包</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">arm64免安装版</a></li>
            </ul>
        </div>
    </div>

    <!-- 历史版本：8.6.5 -->
    <div class="bg-white rounded-xl shadow-md overflow-hidden transition-all duration-300 hover:shadow-lg">
        <div class="vc_toggle_title p-4 cursor-pointer flex justify-between items-center bg-primary/5">
            <h4 class="text-lg font-semibold text-gray-800">历史版本：8.6.5</h4>
            <i class="vc_toggle_icon fa fa-chevron-down text-primary transition-transform duration-300"></i>
        </div>
        <div class="vc_toggle_content px-4 pb-4 active">
            <h3 class="text-xl font-bold mb-2">更新时间：2024.03.30</h3>
            <h3 class="text-xl font-bold mb-2">更新说明：</h3>
            <p>1. 改进对Python 3.12的语法高亮支持。<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;2. 修复自动更新功能的网络连接问题。</p>
            <h3 class="text-xl font-bold mb-2 mt-4">下载链接</h3>
            <ul class="list-disc pl-5 mt-2 space-y-1">
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">64位安装包</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">64位免安装版</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">32位安装包</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">32位免安装版</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">arm64安装包</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">arm64免安装版</a></li>
            </ul>
        </div>
    </div>

    <!-- 历史版本：8.6.3 -->
    <div class="bg-white rounded-xl shadow-md overflow-hidden transition-all duration-300 hover:shadow-lg">
        <div class="vc_toggle_title p-4 cursor-pointer flex justify-between items-center bg-primary/5">
            <h4 class="text-lg font-semibold text-gray-800">历史版本：8.6.3</h4>
            <i class="vc_toggle_icon fa fa-chevron-down text-primary transition-transform duration-300"></i>
        </div>
        <div class="vc_toggle_content px-4 pb-4 active">
            <h3 class="text-xl font-bold mb-2">更新时间：2024.02.19</h3>
            <h3 class="text-xl font-bold mb-2">更新说明：</h3>
            <p>1. 解决插件安装后未正确显示的问题。<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;2. 优化插件管理器的搜索和排序功能。</p>
            <h3 class="text-xl font-bold mb-2 mt-4">下载链接</h3>
            <ul class="list-disc pl-5 mt-2 space-y-1">
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">64位安装包</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">64位免安装版</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">32位安装包</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">32位免安装版</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">arm64安装包</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">arm64免安装版</a></li>
            </ul>
        </div>
    </div>

    <!-- 历史版本：8.6.2 -->
    <div class="bg-white rounded-xl shadow-md overflow-hidden transition-all duration-300 hover:shadow-lg">
        <div class="vc_toggle_title p-4 cursor-pointer flex justify-between items-center bg-primary/5">
            <h4 class="text-lg font-semibold text-gray-800">历史版本：8.6.2</h4>
            <i class="vc_toggle_icon fa fa-chevron-down text-primary transition-transform duration-300"></i>
        </div>
        <div class="vc_toggle_content px-4 pb-4 active">
            <h3 class="text-xl font-bold mb-2">更新时间：2024.01.15</h3>
            <h3 class="text-xl font-bold mb-2">更新说明：</h3>
            <p>1. 减少启动时的资源占用，提升响应速度。<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;2. 支持直接从资源管理器拖放文件到Notepad++。</p>
            <h3 class="text-xl font-bold mb-2 mt-4">下载链接</h3>
            <ul class="list-disc pl-5 mt-2 space-y-1">
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">64位安装包</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">64位免安装版</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">32位安装包</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">32位免安装版</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">arm64安装包</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">arm64免安装版</a></li>
            </ul>
        </div>
    </div>

    <!-- 历史版本：8.6.1 -->
    <div class="bg-white rounded-xl shadow-md overflow-hidden transition-all duration-300 hover:shadow-lg">
        <div class="vc_toggle_title p-4 cursor-pointer flex justify-between items-center bg-primary/5">
            <h4 class="text-lg font-semibold text-gray-800">历史版本：8.6.1</h4>
            <i class="vc_toggle_icon fa fa-chevron-down text-primary transition-transform duration-300"></i>
        </div>
        <div class="vc_toggle_content px-4 pb-4 active">
            <h3 class="text-xl font-bold mb-2">更新时间：2024.01.05</h3>
            <h3 class="text-xl font-bold mb-2">更新说明：</h3>
            <p>1. 新增代码片段管理器，支持自定义代码模板。<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;2. 优化状态栏布局，显示更多编辑状态信息。</p>
            <h3 class="text-xl font-bold mb-2 mt-4">下载链接</h3>
            <ul class="list-disc pl-5 mt-2 space-y-1">
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">64位安装包</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">64位免安装版</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">32位安装包</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">32位免安装版</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">arm64安装包</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">arm64免安装版</a></li>
            </ul>
        </div>
    </div>

    <!-- 历史版本：8.6 -->
    <div class="bg-white rounded-xl shadow-md overflow-hidden transition-all duration-300 hover:shadow-lg">
        <div class="vc_toggle_title p-4 cursor-pointer flex justify-between items-center bg-primary/5">
            <h4 class="text-lg font-semibold text-gray-800">历史版本：8.6</h4>
            <i class="vc_toggle_icon fa fa-chevron-down text-primary transition-transform duration-300"></i>
        </div>
        <div class="vc_toggle_content px-4 pb-4 active">
            <h3 class="text-xl font-bold mb-2">更新时间：2023.11.23</h3>
            <h3 class="text-xl font-bold mb-2">更新说明：</h3>
            <p>1. 新增对Julia语言的语法高亮和代码折叠支持。<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;2. 解决多标签页切换时的偶发崩溃问题。</p>
            <h3 class="text-xl font-bold mb-2 mt-4">下载链接</h3>
            <ul class="list-disc pl-5 mt-2 space-y-1">
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">64位安装包</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">64位免安装版</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">32位安装包</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">32位免安装版</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">arm64安装包</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">arm64免安装版</a></li>
            </ul>
        </div>
    </div>

    <!-- 历史版本：8.5.8 -->
    <div class="bg-white rounded-xl shadow-md overflow-hidden transition-all duration-300 hover:shadow-lg">
        <div class="vc_toggle_title p-4 cursor-pointer flex justify-between items-center bg-primary/5">
            <h4 class="text-lg font-semibold text-gray-800">历史版本：8.5.8</h4>
            <i class="vc_toggle_icon fa fa-chevron-down text-primary transition-transform duration-300"></i>
        </div>
        <div class="vc_toggle_content px-4 pb-4 active">
            <h3 class="text-xl font-bold mb-2">更新时间：2023.10.17</h3>
            <h3 class="text-xl font-bold mb-2">更新说明：</h3>
            <p>1. 增强“查找和替换”对话框的正则表达式调试工具。<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;2. 支持插件的在线更新和版本回退。</p>
            <h3 class="text-xl font-bold mb-2 mt-4">下载链接</h3>
            <ul class="list-disc pl-5 mt-2 space-y-1">
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">64位安装包</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">64位免安装版</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">32位安装包</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">32位免安装版</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">arm64安装包</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">arm64免安装版</a></li>
            </ul>
        </div>
    </div>

    <!-- 历史版本：8.5.7 -->
    <div class="bg-white rounded-xl shadow-md overflow-hidden transition-all duration-300 hover:shadow-lg">
        <div class="vc_toggle_title p-4 cursor-pointer flex justify-between items-center bg-primary/5">
            <h4 class="text-lg font-semibold text-gray-800">历史版本：8.5.7</h4>
            <i class="vc_toggle_icon fa fa-chevron-down text-primary transition-transform duration-300"></i>
        </div>
        <div class="vc_toggle_content px-4 pb-4 active">
            <h3 class="text-xl font-bold mb-2">更新时间：2023.09.08</h3>
            <h3 class="text-xl font-bold mb-2">更新说明：</h3>
            <p>1. 优化大文件编辑时的滚动流畅度。<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;2. 新增侧边栏文件树视图，支持快速导航。</p>
            <h3 class="text-xl font-bold mb-2 mt-4">下载链接</h3>
            <ul class="list-disc pl-5 mt-2 space-y-1">
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">64位安装包</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">64位免安装版</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">32位安装包</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">32位免安装版</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">arm64安装包</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">arm64免安装版</a></li>
            </ul>
        </div>
    </div>

    <!-- 历史版本：8.5.6 -->
    <div class="bg-white rounded-xl shadow-md overflow-hidden transition-all duration-300 hover:shadow-lg">
        <div class="vc_toggle_title p-4 cursor-pointer flex justify-between items-center bg-primary/5">
            <h4 class="text-lg font-semibold text-gray-800">历史版本：8.5.6</h4>
            <i class="vc_toggle_icon fa fa-chevron-down text-primary transition-transform duration-300"></i>
        </div>
        <div class="vc_toggle_content px-4 pb-4 active">
            <h3 class="text-xl font-bold mb-2">更新时间：2023.08.15</h3>
            <h3 class="text-xl font-bold mb-2">更新说明：</h3>
            <p>1. 改进对C# 12的语法高亮支持。<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;2. 解决插件删除后残留配置的问题。</p>
            <h3 class="text-xl font-bold mb-2 mt-4">下载链接</h3>
            <ul class="list-disc pl-5 mt-2 space-y-1">
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">64位安装包</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">64位免安装版</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">32位安装包</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">32位免安装版</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">arm64安装包</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">arm64免安装版</a></li>
            </ul>
        </div>
    </div>

    <!-- 历史版本：8.5.4 -->
    <div class="bg-white rounded-xl shadow-md overflow-hidden transition-all duration-300 hover:shadow-lg">
        <div class="vc_toggle_title p-4 cursor-pointer flex justify-between items-center bg-primary/5">
            <h4 class="text-lg font-semibold text-gray-800">历史版本：8.5.4</h4>
            <i class="vc_toggle_icon fa fa-chevron-down text-primary transition-transform duration-300"></i>
        </div>
        <div class="vc_toggle_content px-4 pb-4 active">
            <h3 class="text-xl font-bold mb-2">更新时间：2023.06.19</h3>
            <h3 class="text-xl font-bold mb-2">更新说明：</h3>
            <p>1. 支持自定义工具栏图标和布局。<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;2. 修复自动保存功能在特定场景下的异常。</p>
            <h3 class="text-xl font-bold mb-2 mt-4">下载链接</h3>
            <ul class="list-disc pl-5 mt-2 space-y-1">
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">64位安装包</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">64位免安装版</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">32位安装包</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">32位免安装版</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">arm64安装包</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">arm64免安装版</a></li>
            </ul>
        </div>
    </div>

    <!-- 历史版本：8.5.3 -->
    <div class="bg-white rounded-xl shadow-md overflow-hidden transition-all duration-300 hover:shadow-lg">
        <div class="vc_toggle_title p-4 cursor-pointer flex justify-between items-center bg-primary/5">
            <h4 class="text-lg font-semibold text-gray-800">历史版本：8.5.3</h4>
            <i class="vc_toggle_icon fa fa-chevron-down text-primary transition-transform duration-300"></i>
        </div>
        <div class="vc_toggle_content px-4 pb-4 active">
            <h3 class="text-xl font-bold mb-2">更新时间：2023.05.06</h3>
            <h3 class="text-xl font-bold mb-2">更新说明：</h3>
            <p>1. 引入代码大纲视图，显示函数和类结构。<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;2. 减少内存泄漏，提升长时间运行稳定性。</p>
            <h3 class="text-xl font-bold mb-2 mt-4">下载链接</h3>
            <ul class="list-disc pl-5 mt-2 space-y-1">
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">64位安装包</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">64位免安装版</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">32位安装包</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">32位免安装版</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">arm64安装包</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">arm64免安装版</a></li>
            </ul>
        </div>
    </div>

    <!-- 历史版本：8.5.2 -->
    <div class="bg-white rounded-xl shadow-md overflow-hidden transition-all duration-300 hover:shadow-lg">
        <div class="vc_toggle_title p-4 cursor-pointer flex justify-between items-center bg-primary/5">
            <h4 class="text-lg font-semibold text-gray-800">历史版本：8.5.2</h4>
            <i class="vc_toggle_icon fa fa-chevron-down text-primary transition-transform duration-300"></i>
        </div>
        <div class="vc_toggle_content px-4 pb-4 active">
            <h3 class="text-xl font-bold mb-2">更新时间：2023.04.05</h3>
            <h3 class="text-xl font-bold mb-2">更新说明：</h3>
            <p>1. 新增对Dart语言的语法高亮和代码折叠支持。<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;2. 解决“在文件中查找”结果显示不全的问题。</p>
            <h3 class="text-xl font-bold mb-2 mt-4">下载链接</h3>
            <ul class="list-disc pl-5 mt-2 space-y-1">
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">64位安装包</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">64位免安装版</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">32位安装包</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">32位免安装版</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">arm64安装包</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">arm64免安装版</a></li>
            </ul>
        </div>
    </div>

    <!-- 历史版本：8.5.1 -->
    <div class="bg-white rounded-xl shadow-md overflow-hidden transition-all duration-300 hover:shadow-lg">
        <div class="vc_toggle_title p-4 cursor-pointer flex justify-between items-center bg-primary/5">
            <h4 class="text-lg font-semibold text-gray-800">历史版本：8.5.1</h4>
            <i class="vc_toggle_icon fa fa-chevron-down text-primary transition-transform duration-300"></i>
        </div>
        <div class="vc_toggle_content px-4 pb-4 active">
            <h3 class="text-xl font-bold mb-2">更新时间：2023.03.05</h3>
            <h3 class="text-xl font-bold mb-2">更新说明：</h3>
            <p>1. 优化“查找”对话框的快捷键映射。<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;2. 修复多实例模式下的配置同步问题。</p>
            <h3 class="text-xl font-bold mb-2 mt-4">下载链接</h3>
            <ul class="list-disc pl-5 mt-2 space-y-1">
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">64位安装包</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">64位免安装版</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">32位安装包</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">32位免安装版</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">arm64安装包</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">arm64免安装版</a></li>
            </ul>
        </div>
    </div>

    <!-- 历史版本：8.4.9 -->
    <div class="bg-white rounded-xl shadow-md overflow-hidden transition-all duration-300 hover:shadow-lg">
        <div class="vc_toggle_title p-4 cursor-pointer flex justify-between items-center bg-primary/5">
            <h4 class="text-lg font-semibold text-gray-800">历史版本：8.4.9</h4>
            <i class="vc_toggle_icon fa fa-chevron-down text-primary transition-transform duration-300"></i>
        </div>
        <div class="vc_toggle_content px-4 pb-4 active">
            <h3 class="text-xl font-bold mb-2">更新时间：2023.01.31</h3>
            <h3 class="text-xl font-bold mb-2">更新说明：</h3>
            <p>1. 优化启动速度，减少初始化时间。<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;2. 解决插件管理器中部分插件无法安装的问题。</p>
            <h3 class="text-xl font-bold mb-2 mt-4">下载链接</h3>
            <ul class="list-disc pl-5 mt-2 space-y-1">
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">64位安装包</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">64位免安装版</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">32位安装包</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">32位免安装版</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">arm64安装包</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">arm64免安装版</a></li>
            </ul>
        </div>
    </div>

    <!-- 历史版本：8.4.8 -->
    <div class="bg-white rounded-xl shadow-md overflow-hidden transition-all duration-300 hover:shadow-lg">
        <div class="vc_toggle_title p-4 cursor-pointer flex justify-between items-center bg-primary/5">
            <h4 class="text-lg font-semibold text-gray-800">历史版本：8.4.8</h4>
            <i class="vc_toggle_icon fa fa-chevron-down text-primary transition-transform duration-300"></i>
        </div>
        <div class="vc_toggle_content px-4 pb-4 active">
            <h3 class="text-xl font-bold mb-2">更新时间：2022.12.24</h3>
            <h3 class="text-xl font-bold mb-2">更新说明：</h3>
            <p>1. 改进对JavaScript ES6+的语法高亮支持。<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;2. 新增主题颜色选择器，支持自定义配色方案。</p>
            <h3 class="text-xl font-bold mb-2 mt-4">下载链接</h3>
            <ul class="list-disc pl-5 mt-2 space-y-1">
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">64位安装包</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">64位免安装版</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">32位安装包</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">32位免安装版</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">arm64安装包</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">arm64免安装版</a></li>
            </ul>
        </div>
    </div>

    <!-- 历史版本：8.4.7 -->
    <div class="bg-white rounded-xl shadow-md overflow-hidden transition-all duration-300 hover:shadow-lg">
        <div class="vc_toggle_title p-4 cursor-pointer flex justify-between items-center bg-primary/5">
            <h4 class="text-lg font-semibold text-gray-800">历史版本：8.4.7</h4>
            <i class="vc_toggle_icon fa fa-chevron-down text-primary transition-transform duration-300"></i>
        </div>
        <div class="vc_toggle_content px-4 pb-4 active">
            <h3 class="text-xl font-bold mb-2">更新时间：2022.11.08</h3>
            <h3 class="text-xl font-bold mb-2">更新说明：</h3>
            <p>1. 解决代码折叠后行号显示异常的问题。<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;2. 修复自动更新功能的下载中断问题。</p>
            <h3 class="text-xl font-bold mb-2 mt-4">下载链接</h3>
            <ul class="list-disc pl-5 mt-2 space-y-1">
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">64位安装包</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">64位免安装版</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">32位安装包</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">32位免安装版</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">arm64安装包</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">arm64免安装版</a></li>
            </ul>
        </div>
    </div>

    <!-- 历史版本：8.4.6 -->
    <div class="bg-white rounded-xl shadow-md overflow-hidden transition-all duration-300 hover:shadow-lg">
        <div class="vc_toggle_title p-4 cursor-pointer flex justify-between items-center bg-primary/5">
            <h4 class="text-lg font-semibold text-gray-800">历史版本：8.4.6</h4>
            <i class="vc_toggle_icon fa fa-chevron-down text-primary transition-transform duration-300"></i>
        </div>
        <div class="vc_toggle_content px-4 pb-4 active">
            <h3 class="text-xl font-bold mb-2">更新时间：2022.09.11</h3>
            <h3 class="text-xl font-bold mb-2">更新说明：</h3>
            <p>1. 支持Markdown文件的实时预览。<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;2. 减少内存占用，提升编辑大文件时的响应速度。</p>
            <h3 class="text-xl font-bold mb-2 mt-4">下载链接</h3>
            <ul class="list-disc pl-5 mt-2 space-y-1">
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">64位安装包</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">64位免安装版</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">32位安装包</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">32位免安装版</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">arm64安装包</a></li>
                <li><a href="https://sourl.cn/vURmL2" class="text-primary hover:underline">arm64免安装版</a></li>
            </ul>
        </div>
    </div>
</body>


<p><center><h3></h3></center></p>
<!-- <p><center><img src="./img/ma.png" alt="Smiley face" width="430" height="430"></center></p> -->
</div>
</div>
</div></div></div></div>


																	</div>
							</article>
													</div><!--/#content-->

				</div><!--/#primary-->
			</div><!--/.col-lg-12-->
		</div><!--/.row-->
	</div><!--/.container.-->
</section><!--/#main-->
<section id="bottom" class="wet-asphalt">
  <div class="container">
    <div class="row">
      <div class="col-sm-12 col-xs-12"><h3></h3>			<div class="textwidget"></div>
		</div>    </div>
  </div>
</section>
<footer id="footer" class="footer-bg" role="contentinfo">
  <div class="container">
    <div class="row">
	      <div class="col-sm-6">
        © 2020<a target="_blank" href="http://www.notepadplus.com.cn/" title="notepad">Notepad</a>本站非notepad开发者官方网站. All Rights Reserved.<script>
var _hmt = _hmt || [];
(function() {
  var hm = document.createElement("script");
  hm.src = "https://hm.baidu.com/hm.js?7224e9e4acdb6fa5155fcf6f10eee6b4";
  var s = document.getElementsByTagName("script")[0];
  s.parentNode.insertBefore(hm, s);
})();
</script>
		      </div>
	        <div class="col-sm-6">
        <ul class="pull-right">
          <li id="menu-item-250" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-home current-menu-item page_item page-item-176 current_page_item menu-item-250 menu-item-active"><a href="http://www.notepadplus.com.cn/">Notepad</a></li>
          <li>
            <a id="gototop" class="gototop" href="http://www.notepadplus.com.cn/#"><i class="fa fa-chevron-up"></i></a>          </li>
        </ul>
      </div>
	      </div>
  </div>
</footer><!--/#footer-->

      </div><!--/#boxed-->


	<script type="text/javascript">
		var c = document.body.className;
		c = c.replace(/woocommerce-no-js/, 'woocommerce-js');
		document.body.className = c;
	</script>
	<link rel="stylesheet" id="font-awesome-css" href="./index_files/font-awesome.min(1).css" type="text/css" media="all">
<script type="text/javascript">
/* <![CDATA[ */
var wpmenucart_ajax = {"ajaxurl":"http:\/\/www.notepadplus.com.cn\/wp-admin\/admin-ajax.php","nonce":"0f434f94e5"};
/* ]]> */
</script>
<script type="text/javascript" src="./index_files/wpmenucart.js"></script>
<script type="text/javascript">
/* <![CDATA[ */
var wpcf7 = {"apiSettings":{"root":"http:\/\/www.notepadplus.com.cn\/wp-json\/contact-form-7\/v1","namespace":"contact-form-7\/v1"}};
/* ]]> */
</script>
<script type="text/javascript" src="./index_files/scripts.js"></script>
<script type="text/javascript" src="./index_files/jquery.themepunch.tools.min.js" defer="defer"></script>
<script type="text/javascript" src="./index_files/jquery.themepunch.revolution.min.js" defer="defer"></script>
<script type="text/javascript" src="./index_files/js.cookie.min.js"></script>
<script type="text/javascript">
/* <![CDATA[ */
var woocommerce_params = {"ajax_url":"\/wp-admin\/admin-ajax.php","wc_ajax_url":"\/?wc-ajax=%%endpoint%%"};
/* ]]> */
</script>
<script type="text/javascript" src="./index_files/woocommerce.min.js"></script>
<script type="text/javascript">
/* <![CDATA[ */
var wc_cart_fragments_params = {"ajax_url":"\/wp-admin\/admin-ajax.php","wc_ajax_url":"\/?wc-ajax=%%endpoint%%","cart_hash_key":"wc_cart_hash_b0dd839f49d6aa5effbccdcbc9f318f4","fragment_name":"wc_fragments_b0dd839f49d6aa5effbccdcbc9f318f4"};
/* ]]> */
</script>
<script type="text/javascript" src="./index_files/cart-fragments.min.js"></script>
<script type="text/javascript" src="./index_files/jquery.background-video.js"></script>
<script type="text/javascript" src="./index_files/jquery.fancybox.min.js"></script>
<script type="text/javascript" src="./index_files/posts-block.js"></script>
<script type="text/javascript" src="./index_files/wooswipe.js"></script>
<script type="text/javascript" src="./index_files/jquery.dlmenu.js"></script>
<script type="text/javascript" src="./index_files/main.js"></script>
<script type="text/javascript">
/* <![CDATA[ */
var lvca_ajax_object = {"ajax_url":"http:\/\/www.notepadplus.com.cn\/wp-admin\/admin-ajax.php"};
/* ]]> */
</script>
<script type="text/javascript" src="./index_files/zhi-frontend.min.js"></script>
<script type="text/javascript" src="./index_files/wp-embed.min.js"></script>
<script type="text/javascript" src="./index_files/js_composer_front.min.js"></script>
<!-- BEGIN # MODAL LOGIN -->
<div class="modal fade" id="login-modal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true" style="display: none;">
    	<div class="modal-dialog">
			<div class="modal-content">
				<div class="modal-header" align="center">

					<button type="button" class="close" data-dismiss="modal" aria-label="Close">
						<i class="fa fa-remove" aria-hidden="true"></i>
					</button>
				</div>

                <!-- Begin # DIV Form -->
                <div id="div-forms">

                    <!-- Begin # Login Form -->
                    <form name="loginform" id="login-form" action="http://www.notepadplus.com.cn/wp-login.php" method="post">
		                <div class="modal-body">
				    		<div id="div-login-msg">
                                <div id="icon-login-msg" class="glyphicon glyphicon-chevron-right"></div>
                                <span id="text-login-msg">输入用户名和密码</span>
                            </div>
				    		<input id="username" name="log" class="form-control" type="text" placeholder="用户名" required="">
				    		<input id="password" name="pwd" class="form-control" type="password" placeholder="密码" required="">
                            <div class="checkbox">
                                <label>
                                    <input name="rememberme" id="rememberme" type="checkbox"> 记住我                                </label>
                            </div>
        		    	</div>
				        <div class="modal-footer">
                            <div>
                                                                        <input type="submit" name="user-submit" value="登录" class="btn btn-primary btn-lg btn-block">
                                        <input type="hidden" name="redirect_to" value="/">
                                        <input type="hidden" name="user-cookie" value="1">
                            </div>
				    	    <div>
                                <button id="login_lost_btn" type="button" class="btn btn-link">忘记密码？</button>
                                <button id="login_register_btn" type="button" class="btn btn-link">注册</button>
                            </div>
				        </div>
                    </form>
                    <!-- End # Login Form -->

                    <!-- Begin | Lost Password Form -->
                    <form id="lost-form" name="lostpasswordform" action="http://www.notepadplus.com.cn/wp-login.php?action=lostpassword" method="post" style="display:none;">
    	    		    <div class="modal-body">
		    				<div id="div-lost-msg">
                                <div id="icon-lost-msg" class="glyphicon glyphicon-chevron-right"></div>
                                <span id="text-lost-msg">输入电子邮箱。</span>
                            </div>
		    				<input id="lost_email" name="user_login" class="form-control" type="text" placeholder="电子邮箱" required="">
            			</div>
		    		    <div class="modal-footer">
						                            <div>
							<input type="submit" name="user-submit" value="发送" class="btn btn-primary btn-lg btn-block">

                            </div>
                            <div>
                                <button id="lost_login_btn" type="button" class="btn btn-link">登陆</button>
                                <button id="lost_register_btn" type="button" class="btn btn-link">注册</button>
                            </div>
		    		    </div>
                    </form>
                    <!-- End | Lost Password Form -->

                    <!-- Begin | Register Form -->
                    <form name="registerform" id="register-form" action="http://www.notepadplus.com.cn/wp-login.php?action=register" method="post" style="display:none;">
            		    <div class="modal-body">
		    				<div id="div-register-msg">
                                <div id="icon-register-msg" class="glyphicon glyphicon-chevron-right"></div>
                                <span id="text-register-msg">注册账户</span>
                            </div>
		    				<input id="register_username" name="user_login" class="form-control" type="text" placeholder="用户名" required="">
                            <input id="register_email" name="user_email" class="form-control" type="text" placeholder="电子邮箱" required="">
                            <input id="register_password" name="password" class="form-control" type="password" placeholder="密码" required="">
            			</div>
		    		    <div class="modal-footer">
                            <div>
														<input type="submit" name="user-submit" value="注册" class="btn btn-primary btn-lg btn-block">
														<input type="hidden" name="redirect_to" value="/?register=true">
							<input type="hidden" name="user-cookie" value="1">
                            </div>
                            <div>
                                <button id="register_login_btn" type="button" class="btn btn-link">登陆</button>
                                <button id="register_lost_btn" type="button" class="btn btn-link">忘记密码？</button>
                            </div>
		    		    </div>
                    </form>
                    <!-- End | Register Form -->

                </div>
                <!-- End # DIV Form -->

			</div>
		</div>
	</div>
    <!-- END # MODAL LOGIN -->
<script type="text/javascript">
jQuery(function($){$(window).scroll(function(){if($(window).scrollTop()>30){$('#header').addClass('affix');$("#wpadminbar").addClass("top-tool-column");$('#header').removeClass('transparent')}else{$('#header').removeClass('affix');$("#wpadminbar").removeClass("top-tool-column");$('#header').addClass('transparent')}})});
</script>
<script type="text/javascript">
jQuery(function($){$(".navsearch a").click(function(){$("#searchform1").fadeToggle();if($(this).find('i').hasClass('fa fa-search')){$(this).find('i').removeClass('fa fa-search');$(this).find('i').addClass('fa fa-remove')}else{$(this).find('i').removeClass('fa fa-remove');$(this).find('i').addClass('fa fa-search')}})});
</script>
<script type="text/javascript">
jQuery(function($){var $formLogin=$('#login-form');var $formLost=$('#lost-form');var $formRegister=$('#register-form');var $divForms=$('#div-forms');var $modalAnimateTime=300;var $msgAnimateTime=150;var $msgShowTime=2000;$('#login_register_btn').click(function(){modalAnimate($formLogin,$formRegister)});$('#register_login_btn').click(function(){modalAnimate($formRegister,$formLogin)});$('#login_lost_btn').click(function(){modalAnimate($formLogin,$formLost)});$('#lost_login_btn').click(function(){modalAnimate($formLost,$formLogin)});$('#lost_register_btn').click(function(){modalAnimate($formLost,$formRegister)});$('#register_lost_btn').click(function(){modalAnimate($formRegister,$formLost)});function modalAnimate($oldForm,$newForm){var $oldH=$oldForm.height();var $newH=$newForm.height();$divForms.css("height",$oldH);$oldForm.fadeToggle($modalAnimateTime,function(){$divForms.animate({height:$newH},$modalAnimateTime,function(){$newForm.fadeToggle($modalAnimateTime)})})}function msgFade($msgId,$msgText){$msgId.fadeOut($msgAnimateTime,function(){$(this).text($msgText).fadeIn($msgAnimateTime)})}function msgChange($divTag,$iconTag,$textTag,$divClass,$iconClass,$msgText){var $msgOld=$divTag.text();msgFade($textTag,$msgText);$divTag.addClass($divClass);$iconTag.removeClass("glyphicon-chevron-right");$iconTag.addClass($iconClass+" "+$divClass);setTimeout(function(){msgFade($textTag,$msgOld);$divTag.removeClass($divClass);$iconTag.addClass("glyphicon-chevron-right");$iconTag.removeClass($iconClass+" "+$divClass)},$msgShowTime)}});
</script>

<style type="text/css">
/* 确保内容始终显示 */
.vc_toggle_content {
    display: block !important;
}
/* 保留标题但移除点击功能 */
.vc_toggle_title {
    cursor: default !important;
}
/* 隐藏展开/折叠图标 */
.vc_toggle_icon {
    display: none !important;
}
</style>

</body></html>