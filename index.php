<?php
ini_set('display_errors','1');
restore_error_handler();
error_reporting(E_ALL);
define('TOKEN', 'omJNpZEhZeHj1ZxFECKkP48B5VFbk1HP');
if (!checkSignature()) {
    exit('签名错误');
}
$HTTP_RAW_POST_DATA or $HTTP_RAW_POST_DATA = file_get_contents('php://input');
if (!$HTTP_RAW_POST_DATA) {
    ob_clean();
    exit($_GET["echostr"]);
}
// file_put_contents("./test.txt",$HTTP_RAW_POST_DATA);
// $HTTP_RAW_POST_DATA =file_get_contents("./test.txt");
$result = wxchatNotify($HTTP_RAW_POST_DATA);
exit($result);
function wxchatNotify($HTTP_RAW_POST_DATA)
    {
        include "reply.php";
        $nowtime = time();
        if (function_exists('libxml_disable_entity_loader')) {
            libxml_disable_entity_loader(true);
        }
        $x = (array) simplexml_load_string($HTTP_RAW_POST_DATA, 'SimpleXMLElement', LIBXML_NOCDATA);
        if ($x['MsgType'] == 'event') {
            //事件
            switch ($x['Event']) {
                case 'CLICK': //点击菜单拉取消息时的事件推送
                    switch ($x['EventKey']) {
                    }
                    break;
                case 'subscribe': //订阅
                    file_put_contents("./auth.txt","1,".$nowtime);
                    return wxchatResponse($x['FromUserName'], $x['ToUserName'], 'text', $replyTxt['subscribe']);
                    break;
                case 'unsubscribe': //取消订阅
                    break;
                case 'SCAN':
                    //扫描二维码[已关注]
                    break;
                case 'LOCATION': //上报地理位置事件
                    break;
                case 'VIEW': //点击菜单跳转链接时的事件推送
                    break;
                case 'TEMPLATESENDJOBFINISH': //模板消息结果
                    break;
                default:
                    break;
            }
        } else {
            //消息
            switch ($x['MsgType']) {
                case 'text': //文本消息
                    if(!empty($replyTxt[$x['Content']])){
                        return wxchatResponse($x['FromUserName'], $x['ToUserName'], 'text', $replyTxt[$x['Content']]);
                    }else{
                        return wxchatResponse($x['FromUserName'], $x['ToUserName'], 'text', "后续能力正在开发中。。。");
                    }
                    break;
                case 'image': //图片消息
                    break;
                case 'voice': //语音消息
                    break;
                case 'video': //视频消息
                    break;
                case 'location': //地理位置消息
                    break;
                case 'link': //链接消息
                    break;
                default:
                    break;
            }
        }
        return '';
    }
     function wxchatResponse($openid, $from, $type, $content, $misc = array())
    {
        $nowtime = time();
        $xml     = '<xml>';
        $xml .= '<ToUserName><![CDATA[' . $openid . ']]></ToUserName>';
        $xml .= '<FromUserName><![CDATA[' . $from . ']]></FromUserName>';
        $xml .= '<CreateTime>' . $nowtime . '</CreateTime>';
        $xml .= '<MsgType><![CDATA[' . $type . ']]></MsgType>';
        switch ($type) {
            case 'text':
                $xml .= '<Content><![CDATA[' . $content . ']]></Content>';
                break;
            case 'image':
                $xml .= '<Image>';
                $xml .= '<MediaId><![CDATA[' . $content . ']]></MediaId>';
                $xml .= '</Image>';
                break;
            case 'voice':
                $xml .= '<Voice>';
                $xml .= '<MediaId><![CDATA[' . $content . ']]></MediaId>';
                $xml .= '</Voice>';
                break;
            case 'video':
                $xml .= '<Video>';
                $xml .= '<MediaId><![CDATA[' . $content . ']]></MediaId>';
                $xml .= '<Title><![CDATA[' . (isset($misc['title']) ? $misc['title'] : '') . ']]></Title>';
                $xml .= '<Description><![CDATA[' . (isset($misc['description']) ? $misc['description'] : '') . ']]></Description>';
                $xml .= '</Video>';
                break;
            case 'music':
                $xml .= '<Music>';
                $xml .= '<MediaId><![CDATA[' . $content . ']]></MediaId>';
                $xml .= '<Title><![CDATA[' . (isset($misc['title']) ? $misc['title'] : '') . ']]></Title>';
                $xml .= '<Description><![CDATA[' . (isset($misc['description']) ? $misc['description'] : '') . ']]></Description>';
                $xml .= '<MusicUrl><![CDATA[' . (isset($misc['musicurl']) ? $misc['musicurl'] : '') . ']]></MusicUrl>';
                $xml .= '<HQMusicUrl><![CDATA[' . (isset($misc['hqmusicurl']) ? $misc['hqmusicurl'] : '') . ']]></HQMusicUrl>';
                $xml .= '<ThumbMediaId><![CDATA[' . (isset($misc['thumb_media_id']) ? $misc['thumb_media_id'] : '') . ']]></ThumbMediaId>';
                $xml .= '</Music>';
                break;
            case 'news':
                if ($misc && count($misc) < 11) {
                    $xml .= '<ArticleCount>' . count($misc) . '</ArticleCount>';
                    $xml .= '<Articles>';
                    foreach ($misc as $k => $v) {
                        $v['title'] = dsubstr($v['title'], 48);
                        $xml .= '<item>';
                        $xml .= '<Title><![CDATA[' . $v['title'] . ']]></Title>';
                        $xml .= '<Description><![CDATA[' . $v['description'] . ']]></Description>';
                        $xml .= '<PicUrl><![CDATA[' . $v['picurl'] . ']]></PicUrl>';
                        $xml .= '<Url><![CDATA[' . $v['url'] . ']]></Url>';
                        $xml .= '</item>';
                    }
                    $xml .= '</Articles>';
                } else {
                    return false;
                }
                break;
            default:
                return false;
                break;
        }
        $xml .= '</xml>';
        return $xml;
    }
function checkSignature()
{
    if (empty($_GET["signature"]) || empty($_GET["timestamp"]) || empty($_GET["nonce"])) {
        return false;
    }
    $signature = $_GET["signature"];
    $timestamp = $_GET["timestamp"];
    $nonce     = $_GET["nonce"];
    $tmpArr    = array(TOKEN, $timestamp, $nonce);
    sort($tmpArr, SORT_STRING);
    $tmpStr = implode($tmpArr);
    $tmpStr = sha1($tmpStr);
    if ($tmpStr == $signature) {
        return true;
    } else {
        return false;
    }
}
