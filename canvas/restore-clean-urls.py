#!/usr/bin/env python3
"""
Script to restore clean URLs to match original online format
Changes href="page.html" back to href="page" to match https://www.better-canvas.com/download format
"""

import os
import re

# Define the mapping to restore clean URLs
link_mappings = {
    'href="download.html"': 'href="download"',
    'href="features.html"': 'href="features"',
    'href="tutorials.html"': 'href="tutorials"',
    'href="themes.html"': 'href="themes"',
    'href="faq.html"': 'href="faq"',
    'href="troubleshooting.html"': 'href="troubleshooting"',
    'href="contact.html"': 'href="contact"',
    'href="changelog.html"': 'href="changelog"',
    'href="privacy.html"': 'href="privacy"',
    'href="terms.html"': 'href="terms"',
    'href="disclaimer.html"': 'href="disclaimer"',
    'href="thank-you.html"': 'href="thank-you"',
}

# Get all HTML files in current directory
html_files = [f for f in os.listdir('.') if f.endswith('.html')]

print(f"🔄 Restoring clean URLs to match original online format...")
print(f"🌐 Target format: https://www.better-canvas.com/download")
print(f"Found {len(html_files)} HTML files to process:")
for file in html_files:
    print(f"  - {file}")

# Process each HTML file
total_changes = 0
for filename in html_files:
    print(f"\n📄 Processing {filename}...")
    
    # Read the file
    with open(filename, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Track changes
    changes_made = 0
    original_content = content
    
    # Apply all link mappings
    for html_link, clean_link in link_mappings.items():
        if html_link in content:
            count_before = content.count(html_link)
            content = content.replace(html_link, clean_link)
            if count_before > 0:
                changes_made += count_before
                print(f"  ✅ Restored {count_before} instances of {html_link} → {clean_link}")
    
    # Write back if changes were made
    if content != original_content:
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"  📝 Updated {filename} - {changes_made} links restored")
        total_changes += changes_made
    else:
        print(f"  ⏭️  No changes needed for {filename}")

print(f"\n🎉 Clean URL restoration complete!")
print(f"📊 Total links restored: {total_changes}")
print(f"✅ All internal links now use clean URL format")
print(f"🌐 Matches original online format: https://www.better-canvas.com/download")
print(f"🔗 Ready for Cloudflare URL handling")
