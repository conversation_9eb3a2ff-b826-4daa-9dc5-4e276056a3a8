#!/usr/bin/env python3
"""
Quick script to update remaining HTML files with clean URLs
"""

import os
import re

# Files to update (excluding already updated ones)
files_to_update = [
    'themes.html',
    'tutorials.html', 
    'faq.html',
    'contact.html',
    'troubleshooting.html',
    'changelog.html',
    'privacy.html',
    'terms.html',
    'disclaimer.html',
    'thank-you.html'
]

# URL mappings for clean URLs
url_mappings = {
    'index.html': '/',
    'download.html': 'download',
    'features.html': 'features', 
    'themes.html': 'themes',
    'tutorials.html': 'tutorials',
    'faq.html': 'faq',
    'contact.html': 'contact',
    'troubleshooting.html': 'troubleshooting',
    'changelog.html': 'changelog',
    'privacy.html': 'privacy',
    'terms.html': 'terms',
    'disclaimer.html': 'disclaimer',
    'thank-you.html': 'thank-you'
}

def update_file_urls(filepath):
    """Update URLs in a single HTML file"""
    if not os.path.exists(filepath):
        print(f"File not found: {filepath}")
        return
    
    with open(filepath, 'r', encoding='utf-8') as f:
        content = f.read()
    
    original_content = content
    
    # Update href attributes
    for old_url, new_url in url_mappings.items():
        # Update href="old_url" to href="new_url"
        content = re.sub(f'href="{old_url}"', f'href="{new_url}"', content)
        # Update href='old_url' to href='new_url'  
        content = re.sub(f"href='{old_url}'", f"href='{new_url}'", content)
    
    # Update og:url meta tags
    content = re.sub(
        r'<meta property="og:url" content="https://www\.better-canvas\.com/([^"]+)\.html">',
        r'<meta property="og:url" content="https://www.better-canvas.com/\1">',
        content
    )
    
    # Add og:url if missing (for pages that don't have it)
    if 'og:url' not in content and 'og:type' in content:
        # Extract filename to determine URL
        filename = os.path.basename(filepath)
        if filename in url_mappings:
            clean_url = url_mappings[filename]
            if clean_url == '/':
                og_url = 'https://www.better-canvas.com/'
            else:
                og_url = f'https://www.better-canvas.com/{clean_url}'
            
            # Insert og:url after og:type
            content = re.sub(
                r'(<meta property="og:type" content="website">)',
                f'\\1\n    <meta property="og:url" content="{og_url}">',
                content
            )
    
    # Only write if content changed
    if content != original_content:
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"Updated: {filepath}")
    else:
        print(f"No changes needed: {filepath}")

def main():
    """Main function to update all files"""
    print("Updating HTML files with clean URLs...")
    
    for filename in files_to_update:
        update_file_urls(filename)
    
    print("URL update complete!")

if __name__ == "__main__":
    main()
