#!/usr/bin/env python3
"""
Script to restore .html extensions to internal links
This reverses the clean URL modifications to work with Cloudflare URL rewrite rules
"""

import os
import re

# Define the mapping of clean URLs back to .html URLs
link_mappings = {
    'href="/"': 'href="index.html"',
    'href="download"': 'href="download.html"',
    'href="features"': 'href="features.html"',
    'href="tutorials"': 'href="tutorials.html"',
    'href="themes"': 'href="themes.html"',
    'href="faq"': 'href="faq.html"',
    'href="troubleshooting"': 'href="troubleshooting.html"',
    'href="contact"': 'href="contact.html"',
    'href="changelog"': 'href="changelog.html"',
    'href="privacy"': 'href="privacy.html"',
    'href="terms"': 'href="terms.html"',
    'href="disclaimer"': 'href="disclaimer.html"',
    'href="thank-you"': 'href="thank-you.html"',
}

# Get all HTML files in current directory
html_files = [f for f in os.listdir('.') if f.endswith('.html')]

print(f"🔄 Restoring .html extensions to internal links...")
print(f"Found {len(html_files)} HTML files to process:")
for file in html_files:
    print(f"  - {file}")

# Process each HTML file
total_changes = 0
for filename in html_files:
    print(f"\n📄 Processing {filename}...")
    
    # Read the file
    with open(filename, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Track changes
    changes_made = 0
    original_content = content
    
    # Apply all link mappings
    for clean_link, html_link in link_mappings.items():
        if clean_link in content:
            count_before = content.count(clean_link)
            content = content.replace(clean_link, html_link)
            count_after = content.count(html_link) - original_content.count(html_link)
            if count_before > 0:
                changes_made += count_before
                print(f"  ✅ Restored {count_before} instances of {clean_link} → {html_link}")
    
    # Write back if changes were made
    if content != original_content:
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"  📝 Updated {filename} - {changes_made} links restored")
        total_changes += changes_made
    else:
        print(f"  ⏭️  No changes needed for {filename}")

print(f"\n🎉 Link restoration complete!")
print(f"📊 Total links restored: {total_changes}")
print(f"✅ All internal links now use .html extensions")
print(f"🌐 Ready for Cloudflare URL rewrite rules")
