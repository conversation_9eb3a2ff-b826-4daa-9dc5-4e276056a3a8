<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Better Canvas Tutorials - Complete Guide & How-to Videos | Better Canvas Extension</title>
    <meta name="description" content="Learn how to use Better Canvas extension with step-by-step tutorials. Master dark mode, better canvas themes, GPA calculator, and all features with our comprehensive guides.">
    <meta name="keywords" content="better canvas tutorials, better canvas guide, how to use better canvas, better canvas themes tutorial, canvas dark mode tutorial, better canvas chrome extension guide, better canvas firefox tutorial">

    <!-- Favicon and Icons -->
    <link rel="icon" type="image/x-icon" href="images/favicon-16x16.ico" sizes="16x16">
    <link rel="icon" type="image/x-icon" href="images/favicon-32x32.ico" sizes="32x32">
    <link rel="icon" type="image/x-icon" href="images/favicon-48x48.ico" sizes="48x48">
    <link rel="icon" type="image/x-icon" href="images/favicon-64x64.ico" sizes="64x64">
    <link rel="shortcut icon" type="image/x-icon" href="images/favicon-32x32.ico">
    <link rel="apple-touch-icon" sizes="64x64" href="images/favicon-64x64.ico">
    <meta name="msapplication-TileImage" content="images/favicon-64x64.ico">
    <meta name="msapplication-TileColor" content="#2563eb">

    <!-- Open Graph -->
    <meta property="og:title" content="Better Canvas Tutorials - Complete Guide & How-to Videos">
    <meta property="og:description" content="Master Better Canvas extension with step-by-step tutorials covering installation, themes, features, and advanced tips.">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://www.better-canvas.com/tutorials">

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- 51.la Analytics -->
    <script charset="UTF-8" id="LA_COLLECT" src="//sdk.51.la/js-sdk-pro.min.js"></script>
    <script>LA.init({id:"KuxQaAm5BOlBxQTS",ck:"KuxQaAm5BOlBxQTS",autoTrack:true,screenRecord:true})</script>

    <!-- Custom CSS for responsive images -->
    <style>
        /* 教程卡片图片 */
        .tutorial-card-img {
            width: 100%;
            height: 192px; /* h-48 */
            object-fit: cover;
            border-radius: 0.75rem 0.75rem 0 0;
        }

        /* 移动端响应式 */
        @media (max-width: 640px) {
            .tutorial-card-img {
                height: 160px;
            }
        }

        /* 平板端优化 */
        @media (min-width: 641px) and (max-width: 1024px) {
            .tutorial-card-img {
                height: 176px;
            }
        }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                        }
                    }
                }
            }
        }
    </script>

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        body { font-family: 'Inter', system-ui, sans-serif; }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .tutorial-card { transition: all 0.3s ease; }
        .tutorial-card:hover { transform: translateY(-4px); }
    </style>

    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-7KZLFW1P0V"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', 'G-7KZLFW1P0V');
    </script>

    <!-- Google AdSense -->
    <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-2379278593682642"
         crossorigin="anonymous"></script>

    <!-- Structured Data - Tutorials (Simplified) -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "LearningResource",
        "name": "Better Canvas Tutorials",
        "description": "Complete tutorials and guides for Better Canvas extension covering installation, themes, features, and advanced usage",
        "learningResourceType": "Tutorial",
        "educationalLevel": "Beginner to Advanced"
    }
    </script>
</head>
<body class="antialiased">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <!-- Logo -->
                <div class="flex items-center">
                    <a href="/" class="flex items-center space-x-2">
                        <div class="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
                            <span class="text-white font-bold text-sm">BC</span>
                        </div>
                        <span class="text-xl font-bold text-gray-900">Better Canvas</span>
                    </a>
                </div>

                <!-- Desktop Navigation -->
                <nav class="hidden md:flex items-center space-x-8">
                    <a href="/" class="text-gray-600 hover:text-gray-900 font-medium transition-colors">Home</a>
                    <a href="download" class="text-gray-600 hover:text-gray-900 font-medium transition-colors">Download</a>
                    <a href="features" class="text-gray-600 hover:text-gray-900 font-medium transition-colors">Features</a>
                    <a href="tutorials" class="text-primary-600 font-medium">Tutorials</a>
                    <a href="themes" class="text-gray-600 hover:text-gray-900 font-medium transition-colors">Themes</a>
                    <a href="faq" class="text-gray-600 hover:text-gray-900 font-medium transition-colors">FAQ</a>
                </nav>

                <!-- CTA Button -->
                <div class="hidden md:flex">
                    <a href="download" class="bg-primary-600 hover:bg-primary-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-200">
                        Download Now
                    </a>
                </div>

                <!-- Mobile menu button -->
                <button class="md:hidden p-2" onclick="toggleMobileMenu()">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                </button>
            </div>

            <!-- Mobile Navigation -->
            <div id="mobile-menu" class="hidden md:hidden py-4 border-t border-gray-200">
                <nav class="flex flex-col space-y-4">
                    <a href="/" class="text-gray-600 hover:text-gray-900 font-medium">Home</a>
                    <a href="download" class="text-gray-600 hover:text-gray-900 font-medium">Download</a>
                    <a href="features" class="text-gray-600 hover:text-gray-900 font-medium">Features</a>
                    <a href="tutorials" class="text-primary-600 font-medium">Tutorials</a>
                    <a href="themes" class="text-gray-600 hover:text-gray-900 font-medium">Themes</a>
                    <a href="faq" class="text-gray-600 hover:text-gray-900 font-medium">FAQ</a>
                </nav>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="py-16 lg:py-24 bg-gradient-to-br from-green-50 to-blue-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <h1 class="text-4xl lg:text-6xl font-bold text-gray-900 leading-tight mb-6">
                    Better Canvas Tutorials
                </h1>
                <p class="text-xl text-gray-600 mb-8 max-w-3xl mx-auto leading-relaxed">
                    Master Better Canvas extension with our comprehensive tutorials. Learn how to install, customize better canvas themes,
                    use advanced features, and get the most out of your Canvas experience.
                </p>

                <!-- Tutorial Stats -->
                <div class="flex flex-col sm:flex-row items-center justify-center space-y-2 sm:space-y-0 sm:space-x-8 mb-12">
                    <div class="flex items-center">
                        <span class="text-3xl font-bold text-green-600 mr-2">15+</span>
                        <span class="text-gray-600 font-medium">Step-by-Step Tutorials</span>
                    </div>
                    <div class="text-gray-600">
                        <span class="font-semibold">Video</span> & Text Guides
                    </div>
                    <div class="text-gray-600">
                        <span class="font-semibold">Beginner</span> Friendly
                    </div>
                </div>

                <!-- Quick Start -->
                <div class="bg-green-100 border border-green-200 rounded-lg p-6 max-w-2xl mx-auto mb-8">
                    <h3 class="font-semibold text-green-900 mb-2">🚀 Quick Start</h3>
                    <p class="text-green-800 text-sm mb-4">
                        New to Better Canvas? Start with our installation tutorial and basic setup guide.
                    </p>
                    <a href="#installation" class="bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-lg transition-colors text-sm">
                        Start Here
                    </a>
                </div>

                <!-- Category Navigation -->
                <div class="flex flex-wrap justify-center gap-3">
                    <a href="#installation" class="bg-white hover:bg-gray-50 text-gray-700 font-medium py-2 px-4 rounded-lg border border-gray-300 transition-colors text-sm">
                        📥 Installation
                    </a>
                    <a href="#themes" class="bg-white hover:bg-gray-50 text-gray-700 font-medium py-2 px-4 rounded-lg border border-gray-300 transition-colors text-sm">
                        🎨 Themes
                    </a>
                    <a href="#features" class="bg-white hover:bg-gray-50 text-gray-700 font-medium py-2 px-4 rounded-lg border border-gray-300 transition-colors text-sm">
                        ⚙️ Features
                    </a>
                    <a href="#advanced" class="bg-white hover:bg-gray-50 text-gray-700 font-medium py-2 px-4 rounded-lg border border-gray-300 transition-colors text-sm">
                        🔧 Advanced
                    </a>
                    <a href="#troubleshooting" class="bg-white hover:bg-gray-50 text-gray-700 font-medium py-2 px-4 rounded-lg border border-gray-300 transition-colors text-sm">
                        🛠️ Troubleshooting
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Installation Tutorials -->
    <section id="installation" class="py-16 lg:py-24 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                    Installation Tutorials
                </h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    Get started with Better Canvas extension in minutes. Follow our step-by-step installation guides for Chrome and Firefox.
                </p>
            </div>

            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Chrome Installation -->
                <div class="tutorial-card bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
                    <div class="relative h-48 overflow-hidden rounded-t-xl">
                        <img
                            src="images/chrome01.png"
                            alt="Chrome Web Store Better Canvas Installation Tutorial"
                            class="w-full h-full object-cover tutorial-card-img"
                            loading="lazy"
                        />
                        <!-- 添加渐变遮罩保持文字可读性 -->
                        <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                        <div class="absolute bottom-4 left-4 text-white">
                            <h3 class="text-xl font-semibold">Chrome Installation</h3>
                        </div>
                        <div class="absolute top-4 right-4 bg-green-500 text-white px-2 py-1 rounded-full text-xs font-semibold">
                            Most Popular
                        </div>
                    </div>

                    <div class="p-6">
                        <p class="text-gray-600 text-sm mb-4">
                            Complete guide to installing Better Canvas chrome extension from Chrome Web Store.
                        </p>

                        <div class="space-y-2 mb-6">
                            <div class="flex items-center text-sm text-gray-600">
                                <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                                Step-by-step screenshots
                            </div>
                            <div class="flex items-center text-sm text-gray-600">
                                <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                                Video walkthrough
                            </div>
                            <div class="flex items-center text-sm text-gray-600">
                                <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                                Troubleshooting tips
                            </div>
                        </div>

                        <div class="flex items-center justify-between mb-4">
                            <span class="text-sm text-gray-500">⏱️ 3 min read</span>
                            <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs font-medium">Beginner</span>
                        </div>

                        <a href="#chrome-tutorial" class="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-4 rounded-lg transition-colors duration-200 block text-center">
                            View Tutorial
                        </a>
                    </div>
                </div>

                <!-- Firefox Installation -->
                <div class="tutorial-card bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
                    <div class="relative h-48 overflow-hidden rounded-t-xl">
                        <img
                            src="images/firefox01.png"
                            alt="Firefox Add-ons Better Canvas Installation Tutorial"
                            class="w-full h-full object-cover tutorial-card-img"
                            loading="lazy"
                        />
                        <!-- 添加渐变遮罩保持文字可读性 -->
                        <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                        <div class="absolute bottom-4 left-4 text-white">
                            <h3 class="text-xl font-semibold">Firefox Installation</h3>
                        </div>
                    </div>

                    <div class="p-6">
                        <p class="text-gray-600 text-sm mb-4">
                            Step-by-step guide to installing Better Canvas firefox extension from Mozilla Add-ons.
                        </p>

                        <div class="space-y-2 mb-6">
                            <div class="flex items-center text-sm text-gray-600">
                                <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                                Firefox-specific steps
                            </div>
                            <div class="flex items-center text-sm text-gray-600">
                                <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                                Permission settings
                            </div>
                            <div class="flex items-center text-sm text-gray-600">
                                <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                                Common issues
                            </div>
                        </div>

                        <div class="flex items-center justify-between mb-4">
                            <span class="text-sm text-gray-500">⏱️ 3 min read</span>
                            <span class="bg-orange-100 text-orange-800 px-2 py-1 rounded text-xs font-medium">Beginner</span>
                        </div>

                        <a href="#firefox-tutorial" class="w-full bg-orange-600 hover:bg-orange-700 text-white font-semibold py-3 px-4 rounded-lg transition-colors duration-200 block text-center">
                            View Tutorial
                        </a>
                    </div>
                </div>

                <!-- First Time Setup -->
                <div class="tutorial-card bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
                    <div class="relative h-48 overflow-hidden rounded-t-xl">
                        <img
                            src="images/chrome02.png"
                            alt="Better Canvas First Time Setup Tutorial - Extension settings and configuration"
                            class="w-full h-full object-cover tutorial-card-img"
                            loading="lazy"
                        />
                        <!-- 添加渐变遮罩保持文字可读性 -->
                        <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                        <div class="absolute bottom-4 left-4 text-white">
                            <div class="text-3xl mb-2">⚙️</div>
                            <h3 class="text-xl font-semibold">First Time Setup</h3>
                        </div>
                        <div class="absolute top-4 right-4 bg-blue-500 text-white px-2 py-1 rounded-full text-xs font-semibold">
                            Essential
                        </div>
                    </div>

                    <div class="p-6">
                        <p class="text-gray-600 text-sm mb-4">
                            Configure Better Canvas extension settings and preferences for the best experience.
                        </p>

                        <div class="space-y-2 mb-6">
                            <div class="flex items-center text-sm text-gray-600">
                                <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                                Initial configuration
                            </div>
                            <div class="flex items-center text-sm text-gray-600">
                                <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                                Recommended settings
                            </div>
                            <div class="flex items-center text-sm text-gray-600">
                                <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                                Privacy options
                            </div>
                        </div>

                        <div class="flex items-center justify-between mb-4">
                            <span class="text-sm text-gray-500">⏱️ 5 min read</span>
                            <span class="bg-green-100 text-green-800 px-2 py-1 rounded text-xs font-medium">Beginner</span>
                        </div>

                        <a href="#setup-tutorial" class="w-full bg-green-600 hover:bg-green-700 text-white font-semibold py-3 px-4 rounded-lg transition-colors duration-200 block text-center">
                            View Tutorial
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Themes Tutorials -->
    <section id="themes" class="py-16 lg:py-24 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                    Better Canvas Themes Tutorials
                </h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    Learn how to use, customize, and create better canvas themes. Transform your Canvas experience with our comprehensive theme guides.
                </p>
            </div>

            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Using Themes -->
                <div class="tutorial-card bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
                    <div class="relative h-48 overflow-hidden rounded-t-xl">
                        <img
                            src="images/50themes.png"
                            alt="Better Canvas Themes Tutorial - How to select and apply different themes"
                            class="w-full h-full object-cover tutorial-card-img"
                            loading="lazy"
                        />
                        <!-- 添加渐变遮罩保持文字可读性 -->
                        <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                        <div class="absolute bottom-4 left-4 text-white">
                            <div class="text-3xl mb-2">🎨</div>
                            <h3 class="text-xl font-semibold">Using Themes</h3>
                        </div>
                        <div class="absolute top-4 right-4 bg-yellow-500 text-white px-2 py-1 rounded-full text-xs font-semibold">
                            Popular
                        </div>
                    </div>

                    <div class="p-6">
                        <p class="text-gray-600 text-sm mb-4">
                            Complete guide to browsing, installing, and switching between better canvas themes.
                        </p>

                        <div class="space-y-2 mb-6">
                            <div class="flex items-center text-sm text-gray-600">
                                <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                                Theme installation
                            </div>
                            <div class="flex items-center text-sm text-gray-600">
                                <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                                Theme switching
                            </div>
                            <div class="flex items-center text-sm text-gray-600">
                                <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                                Theme management
                            </div>
                        </div>

                        <div class="flex items-center justify-between mb-4">
                            <span class="text-sm text-gray-500">⏱️ 4 min read</span>
                            <span class="bg-purple-100 text-purple-800 px-2 py-1 rounded text-xs font-medium">Beginner</span>
                        </div>

                        <a href="#themes-usage" class="w-full bg-purple-600 hover:bg-purple-700 text-white font-semibold py-3 px-4 rounded-lg transition-colors duration-200 block text-center">
                            View Tutorial
                        </a>
                    </div>
                </div>

                <!-- Creating Custom Themes -->
                <div class="tutorial-card bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
                    <div class="relative h-48 overflow-hidden rounded-t-xl">
                        <img
                            src="images/themes02.png"
                            alt="Better Canvas Custom Theme Creation Tutorial - Advanced theme customization guide"
                            class="w-full h-full object-cover tutorial-card-img"
                            loading="lazy"
                        />
                        <!-- 添加渐变遮罩保持文字可读性 -->
                        <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                        <div class="absolute bottom-4 left-4 text-white">
                            <div class="text-3xl mb-2">🛠️</div>
                            <h3 class="text-xl font-semibold">Creating Themes</h3>
                        </div>
                        <div class="absolute top-4 right-4 bg-orange-500 text-white px-2 py-1 rounded-full text-xs font-semibold">
                            Advanced
                        </div>
                    </div>

                    <div class="p-6">
                        <p class="text-gray-600 text-sm mb-4">
                            Learn to create your own better canvas themes using the built-in theme creator.
                        </p>

                        <div class="space-y-2 mb-6">
                            <div class="flex items-center text-sm text-gray-600">
                                <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                                Theme creator tool
                            </div>
                            <div class="flex items-center text-sm text-gray-600">
                                <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                                Color customization
                            </div>
                            <div class="flex items-center text-sm text-gray-600">
                                <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                                Sharing themes
                            </div>
                        </div>

                        <div class="flex items-center justify-between mb-4">
                            <span class="text-sm text-gray-500">⏱️ 8 min read</span>
                            <span class="bg-indigo-100 text-indigo-800 px-2 py-1 rounded text-xs font-medium">Advanced</span>
                        </div>

                        <a href="#theme-creation" class="w-full bg-indigo-600 hover:bg-indigo-700 text-white font-semibold py-3 px-4 rounded-lg transition-colors duration-200 block text-center">
                            View Tutorial
                        </a>
                    </div>
                </div>

                <!-- Dark Mode Setup -->
                <div class="tutorial-card bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
                    <div class="relative">
                        <div class="h-48 bg-gradient-to-br from-gray-800 to-gray-900 p-6 flex items-center justify-center">
                            <div class="text-center text-white">
                                <div class="text-5xl mb-4">🌙</div>
                                <h3 class="text-xl font-semibold">Dark Mode Setup</h3>
                            </div>
                        </div>
                        <div class="absolute top-4 right-4 bg-blue-500 text-white px-2 py-1 rounded-full text-xs font-semibold">
                            Essential
                        </div>
                    </div>

                    <div class="p-6">
                        <p class="text-gray-600 text-sm mb-4">
                            Configure dark mode settings and automatic switching for the best Canvas experience.
                        </p>

                        <div class="space-y-2 mb-6">
                            <div class="flex items-center text-sm text-gray-600">
                                <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                                Enable dark mode
                            </div>
                            <div class="flex items-center text-sm text-gray-600">
                                <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                                Auto-switching
                            </div>
                            <div class="flex items-center text-sm text-gray-600">
                                <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                                Customization options
                            </div>
                        </div>

                        <div class="flex items-center justify-between mb-4">
                            <span class="text-sm text-gray-500">⏱️ 3 min read</span>
                            <span class="bg-gray-100 text-gray-800 px-2 py-1 rounded text-xs font-medium">Beginner</span>
                        </div>

                        <a href="#dark-mode-setup" class="w-full bg-gray-800 hover:bg-gray-900 text-white font-semibold py-3 px-4 rounded-lg transition-colors duration-200 block text-center">
                            View Tutorial
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Tutorials -->
    <section id="features" class="py-16 lg:py-24 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                    Features & Tools Tutorials
                </h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    Master all Better Canvas extension features with detailed tutorials covering GPA calculator, enhanced tools, and productivity features.
                </p>
            </div>

            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- GPA Calculator -->
                <div class="tutorial-card bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
                    <div class="relative h-48 overflow-hidden rounded-t-xl">
                        <img
                            src="images/gpa.png"
                            alt="Better Canvas GPA Calculator Tutorial - How to use the automatic grade calculator"
                            class="w-full h-full object-cover tutorial-card-img"
                            loading="lazy"
                        />
                        <!-- 添加渐变遮罩保持文字可读性 -->
                        <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                        <div class="absolute bottom-4 left-4 text-white">
                            <div class="text-3xl mb-2">📊</div>
                            <h3 class="text-xl font-semibold">GPA Calculator</h3>
                        </div>
                        <div class="absolute top-4 right-4 bg-yellow-500 text-white px-2 py-1 rounded-full text-xs font-semibold">
                            Popular
                        </div>
                    </div>

                    <div class="p-6">
                        <p class="text-gray-600 text-sm mb-4">
                            Learn to use the GPA calculator for tracking grades and planning academic goals.
                        </p>

                        <div class="space-y-2 mb-6">
                            <div class="flex items-center text-sm text-gray-600">
                                <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                                Setup & configuration
                            </div>
                            <div class="flex items-center text-sm text-gray-600">
                                <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                                What-if scenarios
                            </div>
                            <div class="flex items-center text-sm text-gray-600">
                                <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                                Grade tracking
                            </div>
                        </div>

                        <div class="flex items-center justify-between mb-4">
                            <span class="text-sm text-gray-500">⏱️ 6 min read</span>
                            <span class="bg-green-100 text-green-800 px-2 py-1 rounded text-xs font-medium">Intermediate</span>
                        </div>

                        <a href="#gpa-tutorial" class="w-full bg-green-600 hover:bg-green-700 text-white font-semibold py-3 px-4 rounded-lg transition-colors duration-200 block text-center">
                            View Tutorial
                        </a>
                    </div>
                </div>

                <!-- Enhanced Todo List -->
                <div class="tutorial-card bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
                    <div class="relative">
                        <div class="h-48 bg-gradient-to-br from-blue-500 to-cyan-500 p-6 flex items-center justify-center">
                            <div class="text-center text-white">
                                <div class="text-5xl mb-4">✅</div>
                                <h3 class="text-xl font-semibold">Enhanced Todo</h3>
                            </div>
                        </div>
                    </div>

                    <div class="p-6">
                        <p class="text-gray-600 text-sm mb-4">
                            Master the enhanced todo list with priorities, categories, and smart organization.
                        </p>

                        <div class="space-y-2 mb-6">
                            <div class="flex items-center text-sm text-gray-600">
                                <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                                Priority management
                            </div>
                            <div class="flex items-center text-sm text-gray-600">
                                <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                                Custom categories
                            </div>
                            <div class="flex items-center text-sm text-gray-600">
                                <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                                Progress tracking
                            </div>
                        </div>

                        <div class="flex items-center justify-between mb-4">
                            <span class="text-sm text-gray-500">⏱️ 4 min read</span>
                            <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs font-medium">Beginner</span>
                        </div>

                        <a href="#todo-tutorial" class="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-4 rounded-lg transition-colors duration-200 block text-center">
                            View Tutorial
                        </a>
                    </div>
                </div>

                <!-- Smart Reminders -->
                <div class="tutorial-card bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
                    <div class="relative h-48 overflow-hidden rounded-t-xl">
                        <img
                            src="images/dark.png"
                            alt="Better Canvas Smart Reminders Tutorial - Setting up notifications and alerts"
                            class="w-full h-full object-cover tutorial-card-img"
                            loading="lazy"
                        />
                        <!-- 添加渐变遮罩保持文字可读性 -->
                        <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                        <div class="absolute bottom-4 left-4 text-white">
                            <div class="text-3xl mb-2">🔔</div>
                            <h3 class="text-xl font-semibold">Smart Reminders</h3>
                        </div>
                    </div>

                    <div class="p-6">
                        <p class="text-gray-600 text-sm mb-4">
                            Set up intelligent reminders and notifications to never miss important deadlines.
                        </p>

                        <div class="space-y-2 mb-6">
                            <div class="flex items-center text-sm text-gray-600">
                                <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                                Notification setup
                            </div>
                            <div class="flex items-center text-sm text-gray-600">
                                <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                                Custom timing
                            </div>
                            <div class="flex items-center text-sm text-gray-600">
                                <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                                Multiple reminders
                            </div>
                        </div>

                        <div class="flex items-center justify-between mb-4">
                            <span class="text-sm text-gray-500">⏱️ 5 min read</span>
                            <span class="bg-red-100 text-red-800 px-2 py-1 rounded text-xs font-medium">Intermediate</span>
                        </div>

                        <a href="#reminders-tutorial" class="w-full bg-red-600 hover:bg-red-700 text-white font-semibold py-3 px-4 rounded-lg transition-colors duration-200 block text-center">
                            View Tutorial
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Help & Support Section -->
    <section class="py-16 lg:py-24 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                    Need More Help?
                </h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    Can't find the tutorial you're looking for? We're here to help you master Better Canvas extension.
                </p>
            </div>

            <div class="grid md:grid-cols-2 gap-8 mb-12">
                <!-- Community Forum -->
                <div class="bg-white rounded-lg p-6 shadow-sm border border-gray-200 text-center">
                    <div class="text-4xl mb-4">💬</div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Community Forum</h3>
                    <p class="text-gray-600 text-sm mb-4">
                        Ask questions and get help from other users
                    </p>
                    <a href="https://github.com/UseBetterCanvas/bettercanvas/discussions" target="_blank" rel="noopener noreferrer" class="text-primary-600 hover:text-primary-700 font-medium">
                        Join Discussion
                    </a>
                </div>

                <!-- Direct Support -->
                <div class="bg-white rounded-lg p-6 shadow-sm border border-gray-200 text-center">
                    <div class="text-4xl mb-4">📧</div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Direct Support</h3>
                    <p class="text-gray-600 text-sm mb-4">
                        Contact our support team for personalized help
                    </p>
                    <a href="contact" class="text-primary-600 hover:text-primary-700 font-medium">
                        Contact Support
                    </a>
                </div>
            </div>

            <!-- Quick Links -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 text-center">
                <h4 class="font-semibold text-blue-900 mb-4">Quick Access</h4>
                <div class="flex flex-wrap justify-center gap-3">
                    <a href="faq" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors text-sm">
                        FAQ
                    </a>
                    <a href="troubleshooting" class="bg-white hover:bg-gray-50 text-blue-600 font-medium py-2 px-4 rounded-lg border border-blue-300 transition-colors text-sm">
                        Troubleshooting
                    </a>
                    <a href="features" class="bg-white hover:bg-gray-50 text-blue-600 font-medium py-2 px-4 rounded-lg border border-blue-300 transition-colors text-sm">
                        All Features
                    </a>
                    <a href="themes" class="bg-white hover:bg-gray-50 text-blue-600 font-medium py-2 px-4 rounded-lg border border-blue-300 transition-colors text-sm">
                        Browse Themes
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Final CTA Section -->
    <section class="py-16 lg:py-24 gradient-bg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center text-white">
                <h2 class="text-3xl lg:text-4xl font-bold mb-4">
                    Ready to Start Using Better Canvas?
                </h2>
                <p class="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
                    Download Better Canvas extension now and follow our tutorials to transform your Canvas experience with dark mode, better canvas themes, and powerful productivity tools.
                </p>

                <!-- CTA Buttons -->
                <div class="flex flex-col sm:flex-row gap-4 justify-center mb-8">
                    <a href="download" class="bg-white text-gray-900 hover:bg-gray-100 font-semibold py-4 px-8 rounded-lg transition-colors duration-200 inline-flex items-center justify-center text-lg">
                        <img src="https://cdn.jsdelivr.net/gh/simple-icons/simple-icons@v9/icons/googlechrome.svg" alt="Chrome" class="w-6 h-6 mr-3">
                        Better Canvas Chrome Extension
                    </a>
                    <a href="download" class="bg-transparent border-2 border-white text-white hover:bg-white hover:text-gray-900 font-semibold py-4 px-8 rounded-lg transition-colors duration-200 inline-flex items-center justify-center text-lg">
                        <img src="https://cdn.jsdelivr.net/gh/simple-icons/simple-icons@v9/icons/firefox.svg" alt="Firefox" class="w-6 h-6 mr-3 filter brightness-0 invert">
                        Better Canvas Firefox
                    </a>
                </div>

                <!-- Tutorial Categories -->
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm opacity-90">
                    <div class="flex items-center justify-center">
                        <span class="mr-2">📥</span>
                        Installation
                    </div>
                    <div class="flex items-center justify-center">
                        <span class="mr-2">🎨</span>
                        Themes
                    </div>
                    <div class="flex items-center justify-center">
                        <span class="mr-2">⚙️</span>
                        Features
                    </div>
                    <div class="flex items-center justify-center">
                        <span class="mr-2">🛠️</span>
                        Advanced
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div class="grid md:grid-cols-4 gap-8">
                <!-- Brand -->
                <div class="md:col-span-1">
                    <div class="flex items-center space-x-2 mb-4">
                        <div class="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
                            <span class="text-white font-bold text-sm">BC</span>
                        </div>
                        <span class="text-xl font-bold">Better Canvas</span>
                    </div>
                    <p class="text-gray-400 text-sm leading-relaxed">
                        Transform your Canvas learning experience with Better Canvas extension - featuring dark mode, better canvas themes, GPA calculator, and productivity tools.
                    </p>
                </div>

                <!-- Quick Links -->
                <div>
                    <h3 class="font-semibold mb-4">Quick Links</h3>
                    <ul class="space-y-2 text-sm">
                        <li><a href="download" class="text-gray-400 hover:text-white transition-colors">Download</a></li>
                        <li><a href="features" class="text-gray-400 hover:text-white transition-colors">Features</a></li>
                        <li><a href="themes" class="text-gray-400 hover:text-white transition-colors">Themes</a></li>
                        <li><a href="tutorials" class="text-gray-400 hover:text-white transition-colors">Tutorials</a></li>
                    </ul>
                </div>

                <!-- Support -->
                <div>
                    <h3 class="font-semibold mb-4">Support</h3>
                    <ul class="space-y-2 text-sm">
                        <li><a href="faq" class="text-gray-400 hover:text-white transition-colors">FAQ</a></li>
                        <li><a href="troubleshooting" class="text-gray-400 hover:text-white transition-colors">Troubleshooting</a></li>
                        <li><a href="contact" class="text-gray-400 hover:text-white transition-colors">Contact Us</a></li>
                        <li><a href="changelog" class="text-gray-400 hover:text-white transition-colors">Changelog</a></li>
                    </ul>
                </div>

                <!-- Official Links -->
                <div>
                    <h3 class="font-semibold mb-4">Official Links</h3>
                    <ul class="space-y-2 text-sm">
                        <li><a href="https://chrome.google.com/webstore/detail/better-canvas/cndibmoanboadcifjkjbdpjgfedanolh" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white transition-colors">Chrome Store</a></li>
                        <li><a href="https://addons.mozilla.org/en-US/firefox/addon/better-canvas/" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white transition-colors">Firefox Add-ons</a></li>
                        <li><a href="https://github.com/UseBetterCanvas/bettercanvas" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white transition-colors">GitHub</a></li>
                        <li><a href="https://diditupe.dev/bettercanvas/" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white transition-colors">Official Site</a></li>
                    </ul>
                </div>
            </div>

            <!-- Bottom Bar -->
            <div class="border-t border-gray-800 mt-12 pt-8">
                <div class="flex flex-col md:flex-row justify-between items-center">
                    <div class="text-sm text-gray-400 mb-4 md:mb-0">
                        © 2024 Better Canvas Resources. All rights reserved.
                    </div>
                    <div class="flex space-x-6 text-sm">
                        <a href="privacy" class="text-gray-400 hover:text-white transition-colors">Privacy Policy</a>
                        <a href="terms" class="text-gray-400 hover:text-white transition-colors">Terms of Use</a>
                        <a href="disclaimer" class="text-gray-400 hover:text-white transition-colors">Disclaimer</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <script>
        function toggleMobileMenu() {
            const menu = document.getElementById('mobile-menu');
            menu.classList.toggle('hidden');
        }

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>
</body>
</html>
