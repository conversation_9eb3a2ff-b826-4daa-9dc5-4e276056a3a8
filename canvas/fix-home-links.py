#!/usr/bin/env python3
"""
Script to fix home page links in navigation menus
Changes href="index.html" to href="/" for better UX
"""

import os
import re

# Get all HTML files in current directory
html_files = [f for f in os.listdir('.') if f.endswith('.html')]

print(f"🏠 Fixing home page links in navigation menus...")
print(f"Found {len(html_files)} HTML files to process:")
for file in html_files:
    print(f"  - {file}")

# Process each HTML file
total_changes = 0
for filename in html_files:
    print(f"\n📄 Processing {filename}...")
    
    # Read the file
    with open(filename, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Track changes
    changes_made = 0
    original_content = content
    
    # Replace href="index.html" with href="/" 
    # But be careful to only replace navigation links, not other references
    pattern = r'href="index\.html"'
    matches = re.findall(pattern, content)
    
    if matches:
        # Replace all instances
        content = re.sub(pattern, 'href="/"', content)
        changes_made = len(matches)
        print(f"  ✅ Fixed {changes_made} home page links")
    
    # Write back if changes were made
    if content != original_content:
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"  📝 Updated {filename}")
        total_changes += changes_made
    else:
        print(f"  ⏭️  No home page links found in {filename}")

print(f"\n🎉 Home page link fixing complete!")
print(f"📊 Total links fixed: {total_changes}")
print(f"✅ All home page links now point to root directory (/)")
print(f"🌐 Better UX for navigation menus")
