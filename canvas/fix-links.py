#!/usr/bin/env python3
"""
Script to fix internal links in HTML files from absolute paths to relative paths
"""

import os
import re

# Define the mapping of absolute paths to relative paths
link_mappings = {
    'href="/"': 'href="index.html"',
    'href="/download"': 'href="download.html"',
    'href="/features"': 'href="features.html"',
    'href="/tutorials"': 'href="tutorials.html"',
    'href="/themes"': 'href="themes.html"',
    'href="/faq"': 'href="faq.html"',
    'href="/troubleshooting"': 'href="troubleshooting.html"',
    'href="/contact"': 'href="contact.html"',
    'href="/changelog"': 'href="changelog.html"',
    'href="/privacy"': 'href="privacy.html"',
    'href="/terms"': 'href="terms.html"',
    'href="/disclaimer"': 'href="disclaimer.html"',
}

# Get all HTML files in current directory
html_files = [f for f in os.listdir('.') if f.endswith('.html')]

print(f"Found {len(html_files)} HTML files to process:")
for file in html_files:
    print(f"  - {file}")

# Process each HTML file
for filename in html_files:
    print(f"\nProcessing {filename}...")
    
    # Read the file
    with open(filename, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Track changes
    changes_made = 0
    original_content = content
    
    # Apply all link mappings
    for old_link, new_link in link_mappings.items():
        if old_link in content:
            content = content.replace(old_link, new_link)
            changes_made += content.count(new_link) - original_content.count(new_link)
    
    # Write back if changes were made
    if content != original_content:
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"  ✅ Updated {filename} - {changes_made} links fixed")
    else:
        print(f"  ⏭️  No changes needed for {filename}")

print(f"\n🎉 Link fixing complete! All internal links are now relative paths.")
print(f"You can now test the links locally by opening index.html in your browser.")
