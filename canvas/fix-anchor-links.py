#!/usr/bin/env python3
"""
Script to fix remaining tutorials.html anchor links
Changes href="tutorials.html#anchor" to href="tutorials#anchor"
"""

import os
import re

# Get all HTML files in current directory
html_files = [f for f in os.listdir('.') if f.endswith('.html')]

print(f"🔗 Fixing remaining tutorials.html anchor links...")
print(f"Found {len(html_files)} HTML files to process:")
for file in html_files:
    print(f"  - {file}")

# Process each HTML file
total_changes = 0
for filename in html_files:
    print(f"\n📄 Processing {filename}...")
    
    # Read the file
    with open(filename, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Track changes
    changes_made = 0
    original_content = content
    
    # Replace tutorials.html#anchor with tutorials#anchor
    pattern = r'href="tutorials\.html(#[^"]*)"'
    matches = re.findall(pattern, content)
    
    if matches:
        # Replace all instances
        content = re.sub(pattern, r'href="tutorials\1"', content)
        changes_made = len(matches)
        print(f"  ✅ Fixed {changes_made} tutorials anchor links")
        for match in matches:
            print(f"    - tutorials.html{match} → tutorials{match}")
    
    # Write back if changes were made
    if content != original_content:
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"  📝 Updated {filename}")
        total_changes += changes_made
    else:
        print(f"  ⏭️  No tutorials.html anchor links found in {filename}")

print(f"\n🎉 Anchor link fixing complete!")
print(f"📊 Total links fixed: {total_changes}")
print(f"✅ All tutorials anchor links now use clean URL format")
print(f"🌐 Format: tutorials#anchor instead of tutorials.html#anchor")
