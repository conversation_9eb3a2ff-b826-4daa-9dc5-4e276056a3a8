#!/usr/bin/env python3
"""
<PERSON>ript to add Google Analytics code to all HTML files
"""

import os
import re

# Google Analytics code to insert
GA_CODE = '''
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-7KZLFW1P0V"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', 'G-7KZLFW1P0V');
    </script>'''

# List of HTML files to process (excluding index.html and themes.html which are already done)
HTML_FILES = [
    'download.html',
    'features.html',
    'faq.html',
    'contact.html',
    'tutorials.html',
    'troubleshooting.html',
    'changelog.html',
    'privacy.html',
    'terms.html',
    'disclaimer.html',
    'sitemap.html',
    'thank-you.html'
]

def add_analytics_to_file(filename):
    """Add Google Analytics code to a single HTML file"""
    if not os.path.exists(filename):
        print(f"File {filename} not found, skipping...")
        return False
    
    with open(filename, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Check if GA code already exists
    if 'gtag.js?id=G-7KZLFW1P0V' in content:
        print(f"Google Analytics already exists in {filename}, skipping...")
        return False
    
    # Find the position to insert GA code (before </head>)
    head_end_pattern = r'(\s*</head>)'
    match = re.search(head_end_pattern, content)
    
    if not match:
        print(f"Could not find </head> tag in {filename}, skipping...")
        return False
    
    # Insert GA code before </head>
    new_content = content[:match.start()] + GA_CODE + '\n' + content[match.start():]
    
    # Write back to file
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(new_content)
    
    print(f"Added Google Analytics to {filename}")
    return True

def main():
    """Main function to process all HTML files"""
    print("Adding Google Analytics to HTML files...")
    
    success_count = 0
    for filename in HTML_FILES:
        if add_analytics_to_file(filename):
            success_count += 1
    
    print(f"\nCompleted! Added Google Analytics to {success_count} files.")

if __name__ == "__main__":
    main()
