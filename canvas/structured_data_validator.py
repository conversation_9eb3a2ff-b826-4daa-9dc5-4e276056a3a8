#!/usr/bin/env python3
"""
Structured Data Validator for Better Canvas Website
Validates JSON-LD structured data in HTML files
"""

import os
import json
import re
from html.parser import HTMLParser

class StructuredDataExtractor(HTMLParser):
    def __init__(self):
        super().__init__()
        self.structured_data = []
        self.in_script = False
        self.script_type = None
        self.script_content = ""

    def handle_starttag(self, tag, attrs):
        if tag == 'script':
            attrs_dict = dict(attrs)
            if attrs_dict.get('type') == 'application/ld+json':
                self.in_script = True
                self.script_type = 'ld+json'
                self.script_content = ""

    def handle_endtag(self, tag):
        if tag == 'script' and self.in_script:
            if self.script_type == 'ld+json':
                try:
                    data = json.loads(self.script_content.strip())
                    self.structured_data.append(data)
                except json.JSONDecodeError as e:
                    print(f"❌ JSON parsing error: {e}")
            self.in_script = False
            self.script_type = None
            self.script_content = ""

    def handle_data(self, data):
        if self.in_script:
            self.script_content += data

def validate_software_application(data):
    """Validate SoftwareApplication structured data"""
    issues = []
    
    # Required properties for SoftwareApplication
    required_props = ['name', 'description', 'applicationCategory']
    for prop in required_props:
        if prop not in data:
            issues.append(f"Missing required property: {prop}")
    
    # Recommended properties
    recommended_props = ['offers', 'aggregateRating', 'operatingSystem']
    for prop in recommended_props:
        if prop not in data:
            issues.append(f"Missing recommended property: {prop}")
    
    # Validate offers
    if 'offers' in data:
        offers = data['offers']
        if '@type' not in offers or offers['@type'] != 'Offer':
            issues.append("Offers should have @type: 'Offer'")
        if 'price' not in offers:
            issues.append("Offers missing price")
        if 'priceCurrency' not in offers:
            issues.append("Offers missing priceCurrency")
    
    # Validate aggregateRating
    if 'aggregateRating' in data:
        rating = data['aggregateRating']
        if '@type' not in rating or rating['@type'] != 'AggregateRating':
            issues.append("AggregateRating should have @type: 'AggregateRating'")
        if 'ratingValue' not in rating:
            issues.append("AggregateRating missing ratingValue")
        if 'ratingCount' not in rating:
            issues.append("AggregateRating missing ratingCount")
    
    # Validate Better Canvas specific properties
    if 'featureList' in data:
        features = data['featureList']
        expected_features = ['Dark Mode', 'Themes', 'GPA Calculator']
        for feature in expected_features:
            if not any(feature.lower() in f.lower() for f in features):
                issues.append(f"Missing key feature in featureList: {feature}")
    
    return issues

def validate_webpage(data):
    """Validate WebPage structured data"""
    issues = []
    
    required_props = ['name', 'description', 'url']
    for prop in required_props:
        if prop not in data:
            issues.append(f"Missing required property: {prop}")
    
    # Check for breadcrumb
    if 'breadcrumb' not in data:
        issues.append("Missing breadcrumb navigation")
    
    return issues

def validate_organization(data):
    """Validate Organization structured data"""
    issues = []
    
    required_props = ['name', 'url']
    for prop in required_props:
        if prop not in data:
            issues.append(f"Missing required property: {prop}")
    
    # Check for social media links
    if 'sameAs' in data:
        same_as = data['sameAs']
        expected_links = ['github.com', 'chrome.google.com', 'addons.mozilla.org']
        for link_type in expected_links:
            if not any(link_type in link for link in same_as):
                issues.append(f"Missing {link_type} link in sameAs")
    
    return issues

def validate_structured_data_file(filepath):
    """Validate structured data in a single HTML file"""
    print(f"\n📄 Validating {filepath}...")
    
    if not os.path.exists(filepath):
        print(f"❌ File not found: {filepath}")
        return False
    
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        
        parser = StructuredDataExtractor()
        parser.feed(content)
        
        if not parser.structured_data:
            print("⚠️  No structured data found")
            return False
        
        print(f"✅ Found {len(parser.structured_data)} structured data blocks")
        
        all_valid = True
        for i, data in enumerate(parser.structured_data):
            print(f"\n🔍 Block {i+1}:")
            
            # Handle @graph structure
            if '@graph' in data:
                for j, item in enumerate(data['@graph']):
                    print(f"  📊 Graph item {j+1}: {item.get('@type', 'Unknown')}")
                    issues = validate_by_type(item)
                    if issues:
                        print(f"    ❌ Issues found:")
                        for issue in issues:
                            print(f"      • {issue}")
                        all_valid = False
                    else:
                        print(f"    ✅ Valid")
            else:
                print(f"  📊 Type: {data.get('@type', 'Unknown')}")
                issues = validate_by_type(data)
                if issues:
                    print(f"  ❌ Issues found:")
                    for issue in issues:
                        print(f"    • {issue}")
                    all_valid = False
                else:
                    print(f"  ✅ Valid")
        
        return all_valid
        
    except Exception as e:
        print(f"❌ Error validating {filepath}: {e}")
        return False

def validate_by_type(data):
    """Validate structured data based on its type"""
    data_type = data.get('@type')
    
    if data_type == 'SoftwareApplication':
        return validate_software_application(data)
    elif data_type == 'WebPage':
        return validate_webpage(data)
    elif data_type == 'Organization':
        return validate_organization(data)
    elif data_type == 'WebSite':
        # Basic validation for WebSite
        required = ['name', 'url']
        issues = []
        for prop in required:
            if prop not in data:
                issues.append(f"Missing required property: {prop}")
        return issues
    else:
        return [f"Unknown or unvalidated type: {data_type}"]

def main():
    """Main validation function"""
    print("🔍 Better Canvas Structured Data Validator")
    print("=" * 50)
    
    # Files to validate
    html_files = [
        'index.html',
        'download.html', 
        'features.html',
        'themes.html',
        'tutorials.html',
        'faq.html'
    ]
    
    all_valid = True
    for html_file in html_files:
        if os.path.exists(html_file):
            file_valid = validate_structured_data_file(html_file)
            all_valid = all_valid and file_valid
        else:
            print(f"\n📄 {html_file} - File not found")
    
    print("\n" + "=" * 50)
    print("📊 Validation Summary:")
    if all_valid:
        print("✅ All structured data is valid!")
    else:
        print("⚠️  Some issues found - review above for details")
    
    print("\n💡 Testing Tools:")
    print("   • Google Rich Results Test: https://search.google.com/test/rich-results")
    print("   • Schema.org Validator: https://validator.schema.org/")
    print("   • Google Search Console: Monitor rich results performance")

if __name__ == "__main__":
    main()
