/**

 * The MIT License (MIT)

 * 

 * Copyright (c) 2015 BG Stock - html5backgroundvideos.com

 * 

 * Permission is hereby granted, free of charge, to any person obtaining a copy

 * of this software and associated documentation files (the "Software"), to deal

 * in the Software without restriction, including without limitation the rights

 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell

 * copies of the Software, and to permit persons to whom the Software is

 * furnished to do so, subject to the following conditions:

 * 

 * The above copyright notice and this permission notice shall be included in all

 * copies or substantial portions of the Software.

 * 

 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR

 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,

 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE

 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER

 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,

 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE

 * SOFTWARE.

 */



/**

 * Set default positioning as a fallback for if the plugin fails

 */

.jquery-background-video-wrapper {

	position: relative;

	overflow: hidden;

	background-position: center center;

	background-repeat: no-repeat;

	-webkit-background-size: cover;

	   -moz-background-size: cover;

	    	background-size: cover;

}

.jquery-background-video {

	position: absolute;

	min-width: 100%;

	min-height: 100%;

	width: auto;

	height: auto;

	top: 50%;

	left: 50%;

	-o-object-fit: cover;

	   object-fit: cover;

	-webkit-transform: translate(-50%,-50%);

	   -moz-transform: translate(-50%,-50%);

	    -ms-transform: translate(-50%,-50%);

	     -o-transform: translate(-50%,-50%);

	    	transform: translate(-50%,-50%);

}

/**

 * Fade in videos

 * Note the .js class - so non js users still

 * see the video

 */

.js .jquery-background-video {

	opacity: 0;

	-webkit-transition: opacity 300ms linear;

			transition: opacity 300ms linear;

}

.js .jquery-background-video.is-visible {

	opacity: 1;

}



/**

 * Pause/play button

 */ 

.jquery-background-video-pauseplay {

	position: absolute;

	background: transparent !important;

	border: none !important;

	box-shadow: none !important;

	width: 20px;

	height: 20px;

	top: 15px;

	right: 15px;

	padding: 0 !important;

	cursor: pointer;

	outline: none !important;

}

.jquery-background-video-pauseplay span {

	display: none;

}

.jquery-background-video-pauseplay:after,

.jquery-background-video-pauseplay:before {

	content: "";

	position: absolute;

	left: 0;

	top: 0;

	-webkit-transition: all .3s ease;

			transition: all .3s ease;

}

.jquery-background-video-pauseplay.play:before {

	border-top: 10px solid transparent;

	border-bottom: 10px solid transparent;

	border-left: 15px solid #FFF;

}

.jquery-background-video-pauseplay.pause:before,

.jquery-background-video-pauseplay.pause:after {

	border-top: 10px solid #FFF;

	border-bottom: 10px solid #FFF;

	border-left: 5px solid #FFF;

}

.jquery-background-video-pauseplay.pause:after {

	left: 10px;

}