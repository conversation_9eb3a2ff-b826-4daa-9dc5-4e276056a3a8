.zhi-team-members .zhi-team-member .zhi-social-list {

  margin-top: 20px; }

  .zhi-team-members .zhi-team-member .zhi-social-list .zhi-social-list-item {

    display: inline;

    margin: 0 15px 0 0; }

.zhi-team-members .zhi-team-member .zhi-team-member-details {

  font-size: 15px;

  line-height: 24px; }

.zhi-team-members .zhi-team-member .zhi-team-member-text .zhi-title {

  font-size: 18px;

  line-height: 26px;

  letter-spacing: 1px;

  font-weight: bold;

  color: #333;

  text-transform: uppercase;

  clear: none;

  margin-top: 0;

  margin-bottom: 10px; }

.zhi-team-members .zhi-team-member .zhi-team-member-text .zhi-team-member-position {

  font-size: 15px;

  line-height: 24px;

  font-style: italic;

  color: #888;

  margin-bottom: 10px; }



/*-------- Style 1 ----------------*/

.zhi-team-members.zhi-style1 .zhi-team-member-wrapper {

  float: left;

  padding: 10px; }

.zhi-team-members.zhi-style1 .zhi-team-member {

  max-width: 320px;

  margin: 0 auto 40px; }

  .zhi-team-members.zhi-style1 .zhi-team-member .zhi-image-wrapper {

    text-align: center;

    position: relative; }

    .zhi-team-members.zhi-style1 .zhi-team-member .zhi-image-wrapper img {

      max-width: 100%;

      margin: 0 auto 30px;

      border-radius: 50%;

      -webkit-transition: all .3s ease-in-out 0s;

      transition: all .3s ease-in-out 0s; }

    .zhi-team-members.zhi-style1 .zhi-team-member .zhi-image-wrapper .zhi-social-list {

      position: absolute;

      height: 100%;

      width: 100%;

      top: 40%;

      z-index: 2; }

      @media only screen and (max-width: 767px) {

        .zhi-team-members.zhi-style1 .zhi-team-member .zhi-image-wrapper .zhi-social-list {

          position: relative;

          top: 0; } }

      .zhi-team-members.zhi-style1 .zhi-team-member .zhi-image-wrapper .zhi-social-list i {

        font-size: 26px;

        color: #fff;

        opacity: 0;

        -webkit-transition: all .3s ease-in-out 0s;

        transition: all .3s ease-in-out 0s; }

        .zhi-team-members.zhi-style1 .zhi-team-member .zhi-image-wrapper .zhi-social-list i:hover {

          color: #ccc; }

        @media only screen and (max-width: 767px) {

          .zhi-team-members.zhi-style1 .zhi-team-member .zhi-image-wrapper .zhi-social-list i {

            color: inherit;

            opacity: 1; }

            .zhi-team-members.zhi-style1 .zhi-team-member .zhi-image-wrapper .zhi-social-list i:hover {

              color: inherit; } }

  .zhi-team-members.zhi-style1 .zhi-team-member:hover .zhi-image-wrapper img {

    -webkit-filter: brightness(50%);

            filter: brightness(50%); }

    @media only screen and (max-width: 767px) {

      .zhi-team-members.zhi-style1 .zhi-team-member:hover .zhi-image-wrapper img {

        -webkit-filter: brightness(80%);

                filter: brightness(80%); } }

  .zhi-team-members.zhi-style1 .zhi-team-member:hover .zhi-image-wrapper .zhi-social-list i {

    opacity: 1; }

  .zhi-team-members.zhi-style1 .zhi-team-member .zhi-team-member-text {

    text-align: center;

    max-width: 650px; }

    .zhi-team-members.zhi-style1 .zhi-team-member .zhi-team-member-text .zhi-title {

      margin-bottom: 10px; }

  .zhi-team-members.zhi-style1 .zhi-team-member .zhi-social-list {

    margin: 10px auto; }



/*-------- Style 2 ----------------*/

.zhi-team-members.zhi-style2 {

  position: relative;

  max-width: 960px; }

  .zhi-team-members.zhi-style2 .zhi-team-member-wrapper {

    clear: both;

    margin-top: 100px;

    /* Make that flip-flop possible */ }

    .zhi-team-members.zhi-style2 .zhi-team-member-wrapper:first-child {

      margin-top: 0; }

    .zhi-team-members.zhi-style2 .zhi-team-member-wrapper .zhi-image-wrapper {

      float: left;

      position: relative; }

      .zhi-team-members.zhi-style2 .zhi-team-member-wrapper .zhi-image-wrapper img {

        max-width: 320px;

        border-radius: 50%;

        -webkit-transition: all .3s ease-in-out 0s;

        transition: all .3s ease-in-out 0s; }

    .zhi-team-members.zhi-style2 .zhi-team-member-wrapper .zhi-team-member-text {

      margin: 10px 0 0;

      vertical-align: middle;

      padding-top: 20px; }

      .zhi-team-members.zhi-style2 .zhi-team-member-wrapper .zhi-team-member-text .zhi-title {

        margin-bottom: 5px; }

      .zhi-team-members.zhi-style2 .zhi-team-member-wrapper .zhi-team-member-text .zhi-team-member-details {

        margin: 10px 0 10px; }

      .zhi-team-members.zhi-style2 .zhi-team-member-wrapper .zhi-team-member-text .zhi-social-list i {

        font-size: 24px; }

    .zhi-team-members.zhi-style2 .zhi-team-member-wrapper:hover .zhi-image-wrapper img {

      -webkit-filter: brightness(80%);

              filter: brightness(80%); }

  .zhi-team-members.zhi-style2 .zhi-team-member-wrapper:nth-child(odd) .zhi-image-wrapper {

    margin-right: 50px; }

  .zhi-team-members.zhi-style2 .zhi-team-member-wrapper:nth-child(even) .zhi-image-wrapper {

    float: right;

    margin-left: 50px; }

  .zhi-team-members.zhi-style2 .zhi-team-member-wrapper:nth-child(even) .zhi-team-member-text .zhi-title, .zhi-team-members.zhi-style2 .zhi-team-member-wrapper:nth-child(even) .zhi-team-member-text .zhi-team-member-position, .zhi-team-members.zhi-style2 .zhi-team-member-wrapper:nth-child(even) .zhi-team-member-text .zhi-team-member-details, .zhi-team-members.zhi-style2 .zhi-team-member-wrapper:nth-child(even) .zhi-team-member-text .zhi-social-list {

    text-align: right; }



@media only screen and (max-width: 767px) {

  .zhi-team-members.zhi-style2 .zhi-team-member-wrapper {

    margin-top: 75px; }

  .zhi-team-members.zhi-style2 .zhi-team-member .zhi-image-wrapper, .zhi-team-members.zhi-style2 .zhi-team-member .zhi-team-member-text {

    width: 100%;

    float: none; }

  .zhi-team-members.zhi-style2 .zhi-team-member .zhi-image-wrapper {

    text-align: center; }

    .zhi-team-members.zhi-style2 .zhi-team-member .zhi-image-wrapper img {

      margin: 0 auto 20px; }

  .zhi-team-members.zhi-style2 .zhi-team-member .zhi-team-member-text {

    max-width: 400px;

    margin: 0 auto;

    padding-top: 0; }

    .zhi-team-members.zhi-style2 .zhi-team-member .zhi-team-member-text .zhi-title, .zhi-team-members.zhi-style2 .zhi-team-member .zhi-team-member-text .zhi-team-member-position, .zhi-team-members.zhi-style2 .zhi-team-member .zhi-team-member-text .zhi-team-member-details, .zhi-team-members.zhi-style2 .zhi-team-member .zhi-team-member-text .zhi-social-list {

      text-align: center !important; } }

.zhi-dark-bg .zhi-team-members .zhi-team-member .zhi-team-member-details {

  color: #909090; }

.zhi-dark-bg .zhi-team-members .zhi-team-member .zhi-team-member-text .zhi-title {

  color: #e5e5e5; }

.zhi-dark-bg .zhi-team-members .zhi-team-member .zhi-team-member-text .zhi-team-member-position {

  color: #505050; }



/*# sourceMappingURL=style.css.map */