jQuery(function($){$('.counter').counterUp();$('[data-toggle="popover"]').popover({html:true,title:function(){return $("#popover-head").html()},content:function(){return $("#searchform").html()}});$(function(){$('#main-slider.carousel').carousel({interval:8000})});$('.centered').each(function(e){$(this).css('margin-top',($('#main-slider').height()-$(this).height())/2)});$(window).resize(function(){$('.centered').each(function(e){$(this).css('margin-top',($('#main-slider').height()-$(this).height())/2)})});var form=$('.contact-form');form.submit(function(){$this=$(this);$.post($(this).attr('action'),function(data){$this.prev().text(data.message).fadeIn().delay(3000).fadeOut()},'json');return false});$('.gototop').click(function(event){event.preventDefault();$('html, body').animate({scrollTop:$("body").offset().top},500)})});
(function($) {
	var ua = navigator.userAgent.toLowerCase(),
		platform = navigator.platform.toLowerCase(),
		UA = ua.match(/(opera|ie|firefox|chrome|version)[\s\/:]([\w\d\.]+)?.*?(safari|version[\s\/:]([\w\d\.]+)|$)/) || [null, 'unknown', 0],
		mode = UA[1] == 'ie' && document.documentMode;
	var Browser = {
		name: (UA[1] == 'version') ? UA[3] : UA[1],
		Platform: {
			name: ua.match(/ip(?:ad|od|hone)/) ? 'ios' : (ua.match(/(?:webos|android)/) || platform.match(/mac|win|linux/) || ['other'])[0]
		},
	};

	function getOffset(elem) {
		if (elem.getBoundingClientRect && Browser.Platform.name != 'ios') {
			var bound = elem.getBoundingClientRect(),
				html = elem.ownerDocument.documentElement,
				htmlScroll = getScroll(html),
				elemScrolls = getScrolls(elem),
				isFixed = (styleString(elem, 'position') == 'fixed');
			return {
				x: parseInt(bound.left) + elemScrolls.x + ((isFixed) ? 0 : htmlScroll.x) - html.clientLeft,
				y: parseInt(bound.top) + elemScrolls.y + ((isFixed) ? 0 : htmlScroll.y) - html.clientTop
			}
		}
		var element = elem,
			position = {
				x: 0,
				y: 0
			};
		if (isBody(elem)) return position;
		while (element && !isBody(element)) {
			position.x += element.offsetLeft;
			position.y += element.offsetTop;
			if (Browser.name == 'firefox') {
				if (!borderBox(element)) {
					position.x += leftBorder(element);
					position.y += topBorder(element)
				}
				var parent = element.parentNode;
				if (parent && styleString(parent, 'overflow') != 'visible') {
					position.x += leftBorder(parent);
					position.y += topBorder(parent)
				}
			} else if (element != elem && Browser.name == 'safari') {
				position.x += leftBorder(element);
				position.y += topBorder(element)
			}
			element = element.offsetParent
		}
		if (Browser.name == 'firefox' && !borderBox(elem)) {
			position.x -= leftBorder(elem);
			position.y -= topBorder(elem)
		}
		return position
	};

	function getScroll(elem) {
		return {
			x: window.pageXOffset || document.documentElement.scrollLeft,
			y: window.pageYOffset || document.documentElement.scrollTop
		}
	};

	function getScrolls(elem) {
		var element = elem.parentNode,
			position = {
				x: 0,
				y: 0
			};
		while (element && !isBody(element)) {
			position.x += element.scrollLeft;
			position.y += element.scrollTop;
			element = element.parentNode
		}
		return position
	};

	function styleString(element, style) {
		return $(element).css(style)
	};

	function styleNumber(element, style) {
		return parseInt(styleString(element, style)) || 0
	};

	function borderBox(element) {
		return styleString(element, '-moz-box-sizing') == 'border-box'
	};

	function topBorder(element) {
		return styleNumber(element, 'border-top-width')
	};

	function leftBorder(element) {
		return styleNumber(element, 'border-left-width')
	};

	function isBody(element) {
		return (/^(?:body|html)$/i).test(element.tagName)
	};
    $(".fancybox").fancybox();
	$(".various").fancybox({
		maxWidth	: 800,
		maxHeight	: 600,
		fitToView	: false,
		width		: '70%',
		height		: '70%',
		autoSize	: false,
		closeClick	: false,
		openEffect	: 'none',
		closeEffect	: 'none',
		type  : 'iframe',  
	});
	function fix_megamenu_position() {
		$('#primary-menu > li.megamenu-enable').each(function() {
			var $item = $('> ul', this);
			if ($item.length == 0) return;
			var self = $('> ul', this).get(0);
			$item.addClass('without-transition');
			$item.removeClass('megamenu-masonry-inited').removeClass('megamenu-fullwidth').css({
				left: 0,
				width: 'auto',
				height: 'auto'
			});
			$(' > li', $item).css({
				left: 0,
				top: 0
			}).each(function() {
				var old_width = $(this).data('old-width') || -1;
				if (old_width != -1) $(this).width(old_width).data('old-width', -1)
			});
			if ($('#primary-navigation .menu-toggle').is(':visible')) return;
			var $container = $item.closest('nav');			
			var container_width = $container.width();
			var container_padding_left = parseInt($container.css('padding-left'));
			var container_padding_right = parseInt($container.css('padding-right'));
			var megamenu_width = $item.outerWidth();
			var parent_width = $item.parent().outerWidth();
			if (megamenu_width > container_width) {
				megamenu_width = container_width;
				var new_megamenu_width = container_width - parseInt($item.css('padding-left')) - parseInt($item.css('padding-right'));
				$item.addClass('megamenu-fullwidth').width(new_megamenu_width);
				var columns = $item.data('megamenu-columns') || 4;
				var column_width = (new_megamenu_width - (columns - 1) * 31 - container_padding_left - container_padding_right) / columns;
				
				$(' > li', $item).each(function() {
					$(this).data('old-width', $(this).width()).width(column_width)
				})
			}
			if (megamenu_width > parent_width) var left = -(megamenu_width - parent_width) / 2;
			else var left = 0;
			var container_offset = getOffset($container[0]);
			var megamenu_offset = getOffset(self);
			if ((megamenu_offset.x - container_offset.x - container_padding_left + left) < 0) left = -(megamenu_offset.x - container_offset.x - container_padding_left);
			if ((megamenu_offset.x + megamenu_width + left) > (container_offset.x + $container.outerWidth() - container_padding_right)) {
				left -= (megamenu_offset.x + megamenu_width + left) - (container_offset.x + $container.outerWidth() - container_padding_right)
			}
			$item.css('left', left).css('left');
			
			$item.removeClass('without-transition')
		})
	}
	fix_megamenu_position();
	$(function() {
		$('#primary-navigation .submenu-languages').addClass('dl-submenu');
		$('#primary-navigation > ul> li.menu-item-language').addClass('menu-item-parent');
		$('#primary-navigation').dlmenu({
			animationClasses: {
				classin: 'dl-animate-in',
				classout: 'dl-animate-out'
			}
		});
		$(window).resize(function() {
			if ($('#primary-navigation .menu-toggle').is(':visible')) {
				$('#primary-navigation .dl-submenu-disabled').addClass('dl-submenu').removeClass('dl-submenu-disabled');
				$('#primary-menu').removeClass('no-responsive');
				$('#primary-navigation').addClass('responsive');
				fix_megamenu_position()
			} else {
				$('#primary-navigation .dl-submenu').addClass('dl-submenu-disabled').removeClass('dl-submenu');
				$('#primary-menu').addClass('no-responsive');
				$('#primary-navigation').removeClass('responsive');
				fix_megamenu_position()
			}
		})
	})
})(jQuery);