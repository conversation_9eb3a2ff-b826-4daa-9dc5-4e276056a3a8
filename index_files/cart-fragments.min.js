jQuery(function(n){if("undefined"==typeof wc_cart_fragments_params)return!1;var t=!0,o=wc_cart_fragments_params.cart_hash_key;try{t="sessionStorage"in window&&null!==window.sessionStorage,window.sessionStorage.setItem("wc","test"),window.sessionStorage.removeItem("wc"),window.localStorage.setItem("wc","test"),window.localStorage.removeItem("wc")}catch(w){t=!1}function a(){t&&sessionStorage.setItem("wc_cart_created",(new Date).getTime())}function s(e){t&&(localStorage.setItem(o,e),sessionStorage.setItem(o,e))}var e={url:wc_cart_fragments_params.wc_ajax_url.toString().replace("%%endpoint%%","get_refreshed_fragments"),type:"POST",success:function(e){e&&e.fragments&&(n.each(e.fragments,function(e,t){n(e).replaceWith(t)}),t&&(sessionStorage.setItem(wc_cart_fragments_params.fragment_name,JSON.stringify(e.fragments)),s(e.cart_hash),e.cart_hash&&a()),n(document.body).trigger("wc_fragments_refreshed"))}};function r(){n.ajax(e)}if(t){var i=null;n(document.body).on("wc_fragment_refresh updated_wc_div",function(){r()}),n(document.body).on("added_to_cart removed_from_cart",function(e,t,n){var r=sessionStorage.getItem(o);null!==r&&r!==undefined&&""!==r||a(),sessionStorage.setItem(wc_cart_fragments_params.fragment_name,JSON.stringify(t)),s(n)}),n(document.body).on("wc_fragments_refreshed",function(){clearTimeout(i),i=setTimeout(r,864e5)}),n(window).on("storage onstorage",function(e){o===e.originalEvent.key&&localStorage.getItem(o)!==sessionStorage.getItem(o)&&r()}),n(window).on("pageshow",function(e){e.originalEvent.persisted&&(n(".widget_shopping_cart_content").empty(),n(document.body).trigger("wc_fragment_refresh"))});try{var c=n.parseJSON(sessionStorage.getItem(wc_cart_fragments_params.fragment_name)),_=sessionStorage.getItem(o),g=Cookies.get("woocommerce_cart_hash"),m=sessionStorage.getItem("wc_cart_created");if(null!==_&&_!==undefined&&""!==_||(_=""),null!==g&&g!==undefined&&""!==g||(g=""),_&&(null===m||m===undefined||""===m))throw"No cart_created";if(m){var d=1*m+864e5,f=(new Date).getTime();if(d<f)throw"Fragment expired";i=setTimeout(r,d-f)}if(!c||!c["div.widget_shopping_cart_content"]||_!==g)throw"No fragment";n.each(c,function(e,t){n(e).replaceWith(t)}),n(document.body).trigger("wc_fragments_loaded")}catch(w){r()}}else r();0<Cookies.get("woocommerce_items_in_cart")?n(".hide_cart_widget_if_empty").closest(".widget_shopping_cart").show():n(".hide_cart_widget_if_empty").closest(".widget_shopping_cart").hide(),n(document.body).on("adding_to_cart",function(){n(".hide_cart_widget_if_empty").closest(".widget_shopping_cart").show()})});