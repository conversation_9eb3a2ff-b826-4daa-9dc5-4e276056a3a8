@-webkit-keyframes zhi-fade {

  0% {

    opacity: 0; }

  100% {

    opacity: 1; } }

@keyframes zhi-fade {

  0% {

    opacity: 0; }

  100% {

    opacity: 1; } }

/* ---------- General tab styles ---------- */

.zhi-tabs {

  position: relative;

  overflow: hidden; }

  .zhi-tabs .zhi-tab-nav {

    display: -webkit-box;

    display: -ms-flexbox;

    display: flex;

    -webkit-box-orient: horizontal;

    -webkit-box-direction: normal;

        -ms-flex-flow: row wrap;

            flex-flow: row wrap;

    -webkit-box-pack: start;

        -ms-flex-pack: start;

            justify-content: flex-start; }

    .zhi-tabs .zhi-tab-nav .zhi-tab {

      text-align: center;

      -webkit-box-flex: 0;

      box-flex: 0;

      -moz-flex: 0 1 auto;

      -ms-flex: 0 1 auto;

      flex: 0 1 auto; }

      .zhi-tabs .zhi-tab-nav .zhi-tab a {

        display: block;

        text-overflow: ellipsis;

        white-space: normal;

        padding: 10px 40px;

        text-decoration: none;

        border: none;

        margin: 0;

        outline: none;

        -webkit-transition: color .3s ease-in-out 0s;

        transition: color .3s ease-in-out 0s; }

        @media only screen and (max-width: 1024px) {

          .zhi-tabs .zhi-tab-nav .zhi-tab a {

            padding: 10px 25px; } }

      .zhi-tabs .zhi-tab-nav .zhi-tab span.zhi-icon-wrapper span {

        font-size: 32px;

        font-weight: 400;

        vertical-align: middle;

        margin-right: 10px; }

      .zhi-tabs .zhi-tab-nav .zhi-tab span.zhi-image-wrapper img {

        max-width: 24px;

        display: inline-block;

        vertical-align: middle;

        height: auto;

        width: auto;

        padding: 0;

        margin: 0 10px 0 0;

        border: none; }

      .zhi-tabs .zhi-tab-nav .zhi-tab span.zhi-tab-title {

        font-size: 14px;

        line-height: 1;

        font-weight: 700;

        text-transform: uppercase;

        letter-spacing: 1px; }

  .zhi-tabs .zhi-tab-panes {

    position: relative; }

    .zhi-tabs .zhi-tab-panes .zhi-tab-pane {

      padding: 40px;

      display: none;

      overflow: hidden; }

      .zhi-tabs .zhi-tab-panes .zhi-tab-pane.zhi-active {

        display: block;

        -webkit-animation: zhi-fade 0.3s ease-in-out;

                animation: zhi-fade 0.3s ease-in-out; }



/* ----- Fallback for IE 8/9 ----- */

.zhi-no-flexbox .zhi-tab-nav {

  display: block; }

  .zhi-no-flexbox .zhi-tab-nav .zhi-tab {

    min-width: 15%;

    display: inline-block; }



/* ------------- Vertical tab styles ----------------- */

.zhi-tabs.zhi-vertical {

  display: -webkit-box;

  display: -ms-flexbox;

  display: flex; }

  .zhi-tabs.zhi-vertical .zhi-tab-nav {

    -webkit-box-flex: 1;

    box-flex: 1;

    -moz-flex: 1 1 auto;

    -ms-flex: 1 1 auto;

    flex: 1 1 auto;

    -webkit-box-orient: vertical;

    box-orient: vertical;

    -webkit-box-direction: normal;

    box-direction: normal;

    -moz-flex-direction: column;

    flex-direction: column;

    -ms-flex-direction: column;

    -webkit-box-pack: start;

        -ms-flex-pack: start;

            justify-content: flex-start; }

  .zhi-tabs.zhi-vertical .zhi-tab-panes {

    -webkit-box-flex: 4;

    box-flex: 4;

    -moz-flex: 4 1 auto;

    -ms-flex: 4 1 auto;

    flex: 4 1 auto; }

  .zhi-tabs.zhi-vertical.zhi-mobile-layout {

    -webkit-box-orient: vertical;

    box-orient: vertical;

    -webkit-box-direction: normal;

    box-direction: normal;

    -moz-flex-direction: column;

    flex-direction: column;

    -ms-flex-direction: column; }



/* --------- Tab navigation in mobile ------------- */

.zhi-tab-mobile-menu {

  display: none;

  /* Hide on desktop */

  position: absolute;

  top: 23px;

  right: 20px;

  background: transparent;

  border: none;

  z-index: 10; }

  .zhi-tab-mobile-menu i {

    font-size: 18px;

    color: #777;

    font-weight: bold; }



.zhi-tabs.zhi-mobile-layout .zhi-tab-mobile-menu {

  display: block;

  /* Show on mobile only */ }

.zhi-tabs.zhi-mobile-layout .zhi-tab-nav {

  -webkit-box-orient: vertical;

  box-orient: vertical;

  -webkit-box-direction: normal;

  box-direction: normal;

  -moz-flex-direction: column;

  flex-direction: column;

  -ms-flex-direction: column;

  cursor: pointer; }

  .zhi-tabs.zhi-mobile-layout .zhi-tab-nav .zhi-tab {

    text-align: center;

    display: none; }

    .zhi-tabs.zhi-mobile-layout .zhi-tab-nav .zhi-tab.zhi-active {

      display: block; }

.zhi-tabs.zhi-mobile-layout.zhi-mobile-open {

  /* Open all tab navs and change the expand menu button to close button */ }

  .zhi-tabs.zhi-mobile-layout.zhi-mobile-open .zhi-tab-nav .zhi-tab {

    display: block; }

  .zhi-tabs.zhi-mobile-layout.zhi-mobile-open .zhi-tab-mobile-menu i:before {

    content: '\e911'; }



/* ------------- Style 1 ----------------- */

.zhi-tabs.zhi-style1 .zhi-tab-nav .zhi-tab {

  border-left: 1px solid #d9d9d9;

  border-bottom: 1px solid #e2e2e2;

  background: #e9e9e9; }

  .zhi-tabs.zhi-style1 .zhi-tab-nav .zhi-tab:first-child {

    border-left-color: transparent;

    border-radius: 5px 0 0 0; }

  .zhi-tabs.zhi-style1 .zhi-tab-nav .zhi-tab:last-child {

    border-radius: 0 5px 0 0; }

  .zhi-tabs.zhi-style1 .zhi-tab-nav .zhi-tab.zhi-active {

    border-bottom: none;

    background: #f2f2f2; }

  .zhi-tabs.zhi-style1 .zhi-tab-nav .zhi-tab a {

    color: #777; }

    .zhi-tabs.zhi-style1 .zhi-tab-nav .zhi-tab a:hover, .zhi-tabs.zhi-style1 .zhi-tab-nav .zhi-tab a:focus {

      color: #333; }

  .zhi-tabs.zhi-style1 .zhi-tab-nav .zhi-tab.zhi-active a {

    color: #333; }

.zhi-tabs.zhi-style1 .zhi-tab-panes {

  background: #f2f2f2;

  border-radius: 0 4px 4px 4px; }



.zhi-tabs.zhi-style1.zhi-mobile-layout:not(.zhi-mobile-open) .zhi-tab.zhi-active {

  background: #eeeeee; }

.zhi-tabs.zhi-style1.zhi-mobile-layout .zhi-tab {

  border-left: none;

  border-bottom-color: #d9d9d9; }

  .zhi-tabs.zhi-style1.zhi-mobile-layout .zhi-tab:first-child {

    border-radius: 5px 5px 0 0; }

  .zhi-tabs.zhi-style1.zhi-mobile-layout .zhi-tab:last-child {

    border-radius: 0; }

.zhi-tabs.zhi-style1.zhi-mobile-layout .zhi-tab-panes {

  border-radius: 0; }



/* -------- Style 2 ----------- */

.zhi-tabs.zhi-style2 .zhi-tab-nav {

  background: #f2f2f2;

  border-radius: 5px 5px 0 0;

  padding: 0 30px; }

  .zhi-tabs.zhi-style2 .zhi-tab-nav .zhi-tab {

    padding: 20px 10px;

    position: relative; }

    .zhi-tabs.zhi-style2 .zhi-tab-nav .zhi-tab a {

      display: inline-block;

      padding: 5px 20px;

      border-radius: 34px;

      color: #666;

      -webkit-transition: all .3s ease-in-out 0s;

      transition: all .3s ease-in-out 0s; }

      .zhi-tabs.zhi-style2 .zhi-tab-nav .zhi-tab a:hover, .zhi-tabs.zhi-style2 .zhi-tab-nav .zhi-tab a:focus {

        color: #888; }

    .zhi-tabs.zhi-style2 .zhi-tab-nav .zhi-tab.zhi-active:after {

      content: '';

      display: block;

      position: absolute;

      bottom: 0;

      left: 0;

      right: 0;

      width: 8px;

      margin: 0 auto;

      border-left: 8px solid transparent;

      border-right: 8px solid transparent;

      border-bottom: 8px solid #3c3d41; }

    .zhi-tabs.zhi-style2 .zhi-tab-nav .zhi-tab.zhi-active a {

      background: #838d8f;

      color: #fff; }

.zhi-tabs.zhi-style2 .zhi-tab-panes {

  background: #3c3d41;

  border-radius: 0 0 5px 5px; }

  .zhi-tabs.zhi-style2 .zhi-tab-panes .zhi-tab-pane {

    color: #838d8f; }

    .zhi-tabs.zhi-style2 .zhi-tab-panes .zhi-tab-pane h1, .zhi-tabs.zhi-style2 .zhi-tab-panes .zhi-tab-pane h2, .zhi-tabs.zhi-style2 .zhi-tab-panes .zhi-tab-pane h3, .zhi-tabs.zhi-style2 .zhi-tab-panes .zhi-tab-pane h4, .zhi-tabs.zhi-style2 .zhi-tab-panes .zhi-tab-pane h5, .zhi-tabs.zhi-style2 .zhi-tab-panes .zhi-tab-pane h6 {

      color: #fff; }



.zhi-tabs.zhi-style2.zhi-mobile-layout .zhi-tab-mobile-menu {

  top: 27px; }

.zhi-tabs.zhi-style2.zhi-mobile-layout .zhi-tab-nav {

  padding: 0; }

.zhi-tabs.zhi-style2.zhi-mobile-layout.zhi-mobile-open .zhi-tab {

  border-bottom: 1px solid #e2e2e2; }

  .zhi-tabs.zhi-style2.zhi-mobile-layout.zhi-mobile-open .zhi-tab:last-child {

    border-bottom: none; }

.zhi-tabs.zhi-style2.zhi-mobile-layout.zhi-mobile-open .zhi-tab.zhi-active:after {

  display: none; }



.zhi-dark-bg .zhi-tabs.zhi-style2 .zhi-tab-nav .zhi-tab a {

  color: #333; }

  .zhi-dark-bg .zhi-tabs.zhi-style2 .zhi-tab-nav .zhi-tab a:hover, .zhi-dark-bg .zhi-tabs.zhi-style2 .zhi-tab-nav .zhi-tab a:focus {

    color: #666; }

.zhi-dark-bg .zhi-tabs.zhi-style2 .zhi-tab-nav .zhi-tab.zhi-active a {

  background: #aaa;

  color: #fff; }

.zhi-dark-bg .zhi-tabs.zhi-style2 .zhi-tab-nav .zhi-tab.zhi-active:after {

  border-bottom: 8px solid #e7e7e7; }

.zhi-dark-bg .zhi-tabs.zhi-style2 .zhi-tab-panes {

  background: #e7e7e7; }

  .zhi-dark-bg .zhi-tabs.zhi-style2 .zhi-tab-panes .zhi-tab-pane {

    color: #666; }

    .zhi-dark-bg .zhi-tabs.zhi-style2 .zhi-tab-panes .zhi-tab-pane h1, .zhi-dark-bg .zhi-tabs.zhi-style2 .zhi-tab-panes .zhi-tab-pane h2, .zhi-dark-bg .zhi-tabs.zhi-style2 .zhi-tab-panes .zhi-tab-pane h3, .zhi-dark-bg .zhi-tabs.zhi-style2 .zhi-tab-panes .zhi-tab-pane h4, .zhi-dark-bg .zhi-tabs.zhi-style2 .zhi-tab-panes .zhi-tab-pane h5, .zhi-dark-bg .zhi-tabs.zhi-style2 .zhi-tab-panes .zhi-tab-pane h6 {

      color: #333333; }



/* -------- Style 3 ----------- */

.zhi-tabs.zhi-style3 .zhi-tab-nav {

  background: #3c3d41;

  border-radius: 5px 5px 0 0; }

  .zhi-tabs.zhi-style3 .zhi-tab-nav .zhi-tab {

    position: relative;

    border-right: 1px solid #4e4f53; }

    .zhi-tabs.zhi-style3 .zhi-tab-nav .zhi-tab a {

      padding: 10px 30px;

      border-radius: 34px;

      color: #8f8e93;

      -webkit-transition: all .3s ease-in-out 0s;

      transition: all .3s ease-in-out 0s; }

      .zhi-tabs.zhi-style3 .zhi-tab-nav .zhi-tab a:hover, .zhi-tabs.zhi-style3 .zhi-tab-nav .zhi-tab a:focus {

        color: #ccc; }

    .zhi-tabs.zhi-style3 .zhi-tab-nav .zhi-tab.zhi-active:after {

      content: '';

      display: block;

      position: absolute;

      bottom: 0;

      left: 0;

      right: 0;

      width: 8px;

      margin: 0 auto;

      border-left: 8px solid transparent;

      border-right: 8px solid transparent;

      border-bottom: 8px solid #f2f2f2; }

    .zhi-tabs.zhi-style3 .zhi-tab-nav .zhi-tab span.zhi-icon-wrapper span, .zhi-tabs.zhi-style3 .zhi-tab-nav .zhi-tab span.zhi-image-wrapper img {

      margin: 0 auto; }

    .zhi-tabs.zhi-style3 .zhi-tab-nav .zhi-tab span.zhi-tab-title {

      display: none; }

    .zhi-tabs.zhi-style3 .zhi-tab-nav .zhi-tab.zhi-active a {

      color: #eeeeee; }

.zhi-tabs.zhi-style3 .zhi-tab-panes {

  background: #f2f2f2;

  border-radius: 0 0 5px 5px; }



.zhi-tabs.zhi-style3.zhi-mobile-layout .zhi-tab-nav {

  -webkit-box-orient: horizontal;

  box-orient: horizontal;

  -webkit-box-direction: normal;

  box-direction: normal;

  -moz-flex-direction: row;

  flex-direction: row;

  -ms-flex-direction: row;

  padding-right: 60px; }

.zhi-tabs.zhi-style3.zhi-mobile-layout.zhi-mobile-open .zhi-tab {

  border-bottom: 1px solid #4e4f53; }

  .zhi-dark-bg .zhi-tabs.zhi-style3.zhi-mobile-layout.zhi-mobile-open .zhi-tab {

    border-bottom-color: #e5e5e5; }

  .zhi-tabs.zhi-style3.zhi-mobile-layout.zhi-mobile-open .zhi-tab.zhi-active:after {

    display: none; }



.zhi-dark-bg .zhi-tabs.zhi-style3 .zhi-tab-nav {

  background: #fff; }

  .zhi-dark-bg .zhi-tabs.zhi-style3 .zhi-tab-nav .zhi-tab {

    border-right: 1px solid #ececec; }

    .zhi-dark-bg .zhi-tabs.zhi-style3 .zhi-tab-nav .zhi-tab a {

      color: #969696; }

      .zhi-dark-bg .zhi-tabs.zhi-style3 .zhi-tab-nav .zhi-tab a:hover, .zhi-dark-bg .zhi-tabs.zhi-style3 .zhi-tab-nav .zhi-tab a:focus {

        color: #666; }

    .zhi-dark-bg .zhi-tabs.zhi-style3 .zhi-tab-nav .zhi-tab.zhi-active a {

      color: #333; }

    .zhi-dark-bg .zhi-tabs.zhi-style3 .zhi-tab-nav .zhi-tab.zhi-active:after {

      border-bottom: 8px solid #e7e7e7; }

.zhi-dark-bg .zhi-tabs.zhi-style3 .zhi-tab-panes {

  background: #e7e7e7; }

  .zhi-dark-bg .zhi-tabs.zhi-style3 .zhi-tab-panes .zhi-tab-pane {

    color: #666; }

    .zhi-dark-bg .zhi-tabs.zhi-style3 .zhi-tab-panes .zhi-tab-pane h1, .zhi-dark-bg .zhi-tabs.zhi-style3 .zhi-tab-panes .zhi-tab-pane h2, .zhi-dark-bg .zhi-tabs.zhi-style3 .zhi-tab-panes .zhi-tab-pane h3, .zhi-dark-bg .zhi-tabs.zhi-style3 .zhi-tab-panes .zhi-tab-pane h4, .zhi-dark-bg .zhi-tabs.zhi-style3 .zhi-tab-panes .zhi-tab-pane h5, .zhi-dark-bg .zhi-tabs.zhi-style3 .zhi-tab-panes .zhi-tab-pane h6 {

      color: #333; }



/* ----------- Style 4 --------------- */

.zhi-tabs.zhi-style4 {

  background: #f2f2f2;

  border-radius: 5px; }

  .zhi-tabs.zhi-style4 .zhi-tab-nav {

    border-bottom: 1px solid #dddddd;

    margin: 0 40px; }

    .zhi-tabs.zhi-style4 .zhi-tab-nav .zhi-tab {

      position: relative;

      z-index: 1;

      margin-right: 20px; }

      .zhi-tabs.zhi-style4 .zhi-tab-nav .zhi-tab:last-child {

        margin-right: 0; }

      .zhi-tabs.zhi-style4 .zhi-tab-nav .zhi-tab a {

        color: #888;

        padding: 15px 20px; }

      .zhi-tabs.zhi-style4 .zhi-tab-nav .zhi-tab:before {

        content: '';

        position: absolute;

        bottom: 0;

        left: 0;

        width: 100%;

        height: 2px;

        background: transparent;

        -webkit-transition: background .3s ease-in-out 0s;

        transition: background .3s ease-in-out 0s; }

      .zhi-tabs.zhi-style4 .zhi-tab-nav .zhi-tab:hover a {

        color: #565656; }

      .zhi-tabs.zhi-style4 .zhi-tab-nav .zhi-tab.zhi-active a {

        color: #333; }

      .zhi-tabs.zhi-style4 .zhi-tab-nav .zhi-tab.zhi-active:before {

        background: #f94213;

        height: 2px; }

  .zhi-tabs.zhi-style4 .zhi-tab-pane {

    padding: 40px; }



.zhi-tabs.zhi-style4.zhi-mobile-layout .zhi-tab-nav {

  cursor: pointer;

  padding: 0;

  margin: 0;

  border: none; }

  .zhi-tabs.zhi-style4.zhi-mobile-layout .zhi-tab-nav .zhi-tab {

    margin: 0;

    border-bottom: 1px solid #e0e0e0; }

    .zhi-dark-bg .zhi-tabs.zhi-style4.zhi-mobile-layout .zhi-tab-nav .zhi-tab {

      border-left: 1px solid #404040;

      border-right: 1px solid #404040;

      border-bottom-color: #404040; }

    .zhi-dark-bg .zhi-tabs.zhi-style4.zhi-mobile-layout .zhi-tab-nav .zhi-tab:first-child {

      border-top: 1px solid #404040; }

    .zhi-tabs.zhi-style4.zhi-mobile-layout .zhi-tab-nav .zhi-tab a {

      padding: 10px 25px; }

    .zhi-tabs.zhi-style4.zhi-mobile-layout .zhi-tab-nav .zhi-tab:before {

      display: none; }

.zhi-tabs.zhi-style4.zhi-mobile-layout.zhi-mobile-open .zhi-tab.zhi-active {

  border-left: 2px solid #f94213;

  border-right: 2px solid #f94213; }

.zhi-dark-bg .zhi-tabs.zhi-style4.zhi-mobile-layout:not(.zhi-mobile-open) .zhi-tab.zhi-active {

  border-top: 1px solid #404040; }



.zhi-dark-bg .zhi-tabs.zhi-style4 {

  background: transparent; }

  .zhi-dark-bg .zhi-tabs.zhi-style4 .zhi-tab-nav {

    margin: 0;

    border-bottom: 1px solid #2a2a2a; }

    .zhi-dark-bg .zhi-tabs.zhi-style4 .zhi-tab-nav .zhi-tab a {

      color: #707070; }

    .zhi-dark-bg .zhi-tabs.zhi-style4 .zhi-tab-nav .zhi-tab:hover a {

      color: #b0b0b0; }

    .zhi-dark-bg .zhi-tabs.zhi-style4 .zhi-tab-nav .zhi-tab.zhi-active a {

      color: #e5e5e5; }

  .zhi-dark-bg .zhi-tabs.zhi-style4 .zhi-tab-pane {

    padding: 40px 0 0;

    color: #909090; }

    .zhi-dark-bg .zhi-tabs.zhi-style4 .zhi-tab-pane h1, .zhi-dark-bg .zhi-tabs.zhi-style4 .zhi-tab-pane h2, .zhi-dark-bg .zhi-tabs.zhi-style4 .zhi-tab-pane h3, .zhi-dark-bg .zhi-tabs.zhi-style4 .zhi-tab-pane h4, .zhi-dark-bg .zhi-tabs.zhi-style4 .zhi-tab-pane h5, .zhi-dark-bg .zhi-tabs.zhi-style4 .zhi-tab-pane h6 {

      color: #e5e5e5; }



/* ----------- Style 5 --------------- */

.zhi-tabs.zhi-style5 .zhi-tab-nav a {

  padding: 20px 50px;

  color: #777;

  position: relative;

  z-index: 1; }

  .zhi-tabs.zhi-style5 .zhi-tab-nav a:after {

    position: absolute;

    top: 0;

    left: 0;

    z-index: -1;

    width: 100%;

    height: 100%;

    background-color: #e9e9e9;

    content: '';

    -webkit-transition: -webkit-transform 0.3s, background-color 0.3s;

    -webkit-transition: background-color 0.3s, -webkit-transform 0.3s;

    transition: background-color 0.3s, -webkit-transform 0.3s;

    transition: transform 0.3s, background-color 0.3s;

    transition: transform 0.3s, background-color 0.3s, -webkit-transform 0.3s;

    -webkit-transform: perspective(900px) rotate3d(1, 0, 0, 90deg);

    transform: perspective(900px) rotate3d(1, 0, 0, 90deg);

    -webkit-transform-origin: 50% 100%;

    transform-origin: 50% 100%;

    -webkit-perspective-origin: 50% 100%;

    perspective-origin: 50% 100%; }

  .zhi-tabs.zhi-style5 .zhi-tab-nav a:hover, .zhi-tabs.zhi-style5 .zhi-tab-nav a:focus {

    color: #333; }

.zhi-tabs.zhi-style5 .zhi-tab-nav .zhi-tab.zhi-active a {

  color: #333; }

  .zhi-tabs.zhi-style5 .zhi-tab-nav .zhi-tab.zhi-active a:after {

    background-color: #f2f2f2;

    -webkit-transform: perspective(900px) rotate3d(1, 0, 0, 0deg);

    transform: perspective(900px) rotate3d(1, 0, 0, 0deg); }

.zhi-tabs.zhi-style5 .zhi-tab-panes {

  background: #f2f2f2; }



.zhi-tabs.zhi-style5.zhi-mobile-layout .zhi-tab-nav .zhi-tab {

  background: #f2f2f2;

  border-bottom: 1px solid #e5e5e5; }



.zhi-dark-bg .zhi-tabs.zhi-style5 .zhi-tab-nav .zhi-tab a {

  color: #b0b0b0; }

  .zhi-dark-bg .zhi-tabs.zhi-style5 .zhi-tab-nav .zhi-tab a:hover, .zhi-dark-bg .zhi-tabs.zhi-style5 .zhi-tab-nav .zhi-tab a:focus {

    color: #dddddd; }

.zhi-dark-bg .zhi-tabs.zhi-style5 .zhi-tab-nav .zhi-tab.zhi-active a {

  color: #333; }



/* ------------- Style 6 and Vertical Style 7 ----------------- */

.zhi-tabs.zhi-style6 .zhi-tab-nav .zhi-tab, .zhi-tabs.zhi-style7 .zhi-tab-nav .zhi-tab {

  text-align: left; }

  .zhi-tabs.zhi-style6 .zhi-tab-nav .zhi-tab a, .zhi-tabs.zhi-style7 .zhi-tab-nav .zhi-tab a {

    padding: 5px 2px;

    color: #666;

    -webkit-transition: all .3s ease-in-out 0s;

    transition: all .3s ease-in-out 0s;

    border-top: 2px solid transparent;

    border-bottom: 2px solid transparent;

    display: inline-block; }

    .zhi-tabs.zhi-style6 .zhi-tab-nav .zhi-tab a:hover, .zhi-tabs.zhi-style6 .zhi-tab-nav .zhi-tab a:focus, .zhi-tabs.zhi-style7 .zhi-tab-nav .zhi-tab a:hover, .zhi-tabs.zhi-style7 .zhi-tab-nav .zhi-tab a:focus {

      color: #333333; }

  .zhi-tabs.zhi-style6 .zhi-tab-nav .zhi-tab.zhi-active a, .zhi-tabs.zhi-style7 .zhi-tab-nav .zhi-tab.zhi-active a {

    border-color: #f94213;

    color: #333; }

.zhi-tabs.zhi-style6 .zhi-tab-pane, .zhi-tabs.zhi-style7 .zhi-tab-pane {

  padding: 40px 0 0; }



.zhi-tabs.zhi-style6 .zhi-tab-nav {

  margin: 0 auto;

  text-align: left; }

  .zhi-tabs.zhi-style6 .zhi-tab-nav .zhi-tab {

    margin-right: 50px; }

    .zhi-tabs.zhi-style6 .zhi-tab-nav .zhi-tab:last-child {

      margin-right: 0; }

.zhi-tabs.zhi-style6 .zhi-tab-pane {

  padding: 40px 0 0; }



.zhi-tabs.zhi-style7 .zhi-tab-nav .zhi-tab {

  padding: 0 25px 0 0; }

  .zhi-tabs.zhi-style7 .zhi-tab-nav .zhi-tab a {

    max-width: none;

    margin: 6px 0; }

    @media only screen and (max-width: 479px) {

      .zhi-tabs.zhi-style7 .zhi-tab-nav .zhi-tab a {

        text-align: center; } }

.zhi-tabs.zhi-style7 .zhi-tab-panes {

  -webkit-box-flex: 6;

  box-flex: 6;

  -moz-flex: 6 1 auto;

  -ms-flex: 6 1 auto;

  flex: 6 1 auto; }

  .zhi-tabs.zhi-style7 .zhi-tab-panes .zhi-tab-pane {

    padding: 0 0 0 20px; }



.zhi-tabs.zhi-style6.zhi-mobile-layout .zhi-tab-mobile-menu, .zhi-tabs.zhi-style7.zhi-mobile-layout .zhi-tab-mobile-menu {

  top: 22px; }

.zhi-tabs.zhi-style6.zhi-mobile-layout .zhi-tab-nav .zhi-tab, .zhi-tabs.zhi-style7.zhi-mobile-layout .zhi-tab-nav .zhi-tab {

  padding: 12px 0;

  width: 100%;

  text-align: center; }

  .zhi-tabs.zhi-style6.zhi-mobile-layout .zhi-tab-nav .zhi-tab a, .zhi-tabs.zhi-style7.zhi-mobile-layout .zhi-tab-nav .zhi-tab a {

    margin: 0; }

.zhi-tabs.zhi-style6.zhi-mobile-layout .zhi-tab-pane, .zhi-tabs.zhi-style7.zhi-mobile-layout .zhi-tab-pane {

  padding: 30px 0 0; }



.zhi-dark-bg .zhi-tabs.zhi-style6 .zhi-tab-nav .zhi-tab a, .zhi-dark-bg .zhi-tabs.zhi-style7 .zhi-tab-nav .zhi-tab a {

  color: #b0b0b0; }

.zhi-dark-bg .zhi-tabs.zhi-style6 .zhi-tab-nav .zhi-tab:hover a, .zhi-dark-bg .zhi-tabs.zhi-style7 .zhi-tab-nav .zhi-tab:hover a {

  color: #dddddd; }

.zhi-dark-bg .zhi-tabs.zhi-style6 .zhi-tab-nav .zhi-tab.zhi-active a, .zhi-dark-bg .zhi-tabs.zhi-style7 .zhi-tab-nav .zhi-tab.zhi-active a {

  color: #eaeaea; }

.zhi-dark-bg .zhi-tabs.zhi-style6 .zhi-tab-pane, .zhi-dark-bg .zhi-tabs.zhi-style7 .zhi-tab-pane {

  color: #909090; }

  .zhi-dark-bg .zhi-tabs.zhi-style6 .zhi-tab-pane h1, .zhi-dark-bg .zhi-tabs.zhi-style6 .zhi-tab-pane h2, .zhi-dark-bg .zhi-tabs.zhi-style6 .zhi-tab-pane h3, .zhi-dark-bg .zhi-tabs.zhi-style6 .zhi-tab-pane h4, .zhi-dark-bg .zhi-tabs.zhi-style6 .zhi-tab-pane h5, .zhi-dark-bg .zhi-tabs.zhi-style6 .zhi-tab-pane h6, .zhi-dark-bg .zhi-tabs.zhi-style7 .zhi-tab-pane h1, .zhi-dark-bg .zhi-tabs.zhi-style7 .zhi-tab-pane h2, .zhi-dark-bg .zhi-tabs.zhi-style7 .zhi-tab-pane h3, .zhi-dark-bg .zhi-tabs.zhi-style7 .zhi-tab-pane h4, .zhi-dark-bg .zhi-tabs.zhi-style7 .zhi-tab-pane h5, .zhi-dark-bg .zhi-tabs.zhi-style7 .zhi-tab-pane h6 {

    color: #e5e5e5; }



/* ------------- Vertical Style 8 ----------------- */

.zhi-tabs.zhi-style8 .zhi-tab-nav .zhi-tab {

  margin: 2px 0; }

  .zhi-tabs.zhi-style8 .zhi-tab-nav .zhi-tab a {

    padding: 10px 30px;

    border-radius: 4px;

    background: #f2f2f2;

    color: #777777;

    -webkit-transition: all .3s ease-in-out 0s;

    transition: all .3s ease-in-out 0s;

    border-left: 3px solid transparent;

    text-align: left; }

    .zhi-tabs.zhi-style8 .zhi-tab-nav .zhi-tab a:hover, .zhi-tabs.zhi-style8 .zhi-tab-nav .zhi-tab a:focus {

      color: #333333; }

  .zhi-tabs.zhi-style8 .zhi-tab-nav .zhi-tab.zhi-active a {

    color: #333;

    border-left-color: #f94213; }

.zhi-tabs.zhi-style8 .zhi-tab-pane {

  padding: 0 0 0 40px; }



.zhi-tabs.zhi-style8.zhi-mobile-layout .zhi-tab-mobile-menu {

  top: 18px; }

.zhi-tabs.zhi-style8.zhi-mobile-layout .zhi-tab-nav .zhi-tab a {

  text-align: left; }

.zhi-tabs.zhi-style8.zhi-mobile-layout:not(.zhi-mobile-open) .zhi-tab.zhi-active a {

  border-color: transparent !important; }

.zhi-tabs.zhi-style8.zhi-mobile-layout .zhi-tab-pane {

  padding: 30px 0 0; }



.zhi-dark-bg .zhi-tabs.zhi-style8 .zhi-tab-pane {

  color: #909090; }

  .zhi-dark-bg .zhi-tabs.zhi-style8 .zhi-tab-pane h1, .zhi-dark-bg .zhi-tabs.zhi-style8 .zhi-tab-pane h2, .zhi-dark-bg .zhi-tabs.zhi-style8 .zhi-tab-pane h3, .zhi-dark-bg .zhi-tabs.zhi-style8 .zhi-tab-pane h4, .zhi-dark-bg .zhi-tabs.zhi-style8 .zhi-tab-pane h5, .zhi-dark-bg .zhi-tabs.zhi-style8 .zhi-tab-pane h6 {

    color: #e5e5e5; }



/* ------------- Vertical Style 9 ----------------- */

.zhi-tabs.zhi-style9 {

  background: #f2f2f2;

  border-radius: 5px; }

  .zhi-tabs.zhi-style9 .zhi-tab-nav {

    border-right: 1px solid #dddddd; }

    .zhi-tabs.zhi-style9 .zhi-tab-nav .zhi-tab {

      border-bottom: 1px solid #d8d8d8;

      background: #e9e9e9; }

      .zhi-tabs.zhi-style9 .zhi-tab-nav .zhi-tab.zhi-active {

        margin-right: -1px;

        background: #f2f2f2; }

      .zhi-tabs.zhi-style9 .zhi-tab-nav .zhi-tab a {

        padding: 10px 30px;

        color: #777; }

        .zhi-tabs.zhi-style9 .zhi-tab-nav .zhi-tab a:hover, .zhi-tabs.zhi-style9 .zhi-tab-nav .zhi-tab a:focus {

          color: #333; }

      .zhi-tabs.zhi-style9 .zhi-tab-nav .zhi-tab.zhi-active a {

        color: #333; }

  .zhi-tabs.zhi-style9 .zhi-tab-panes {

    -webkit-box-flex: 5;

    box-flex: 5;

    -moz-flex: 5 1 auto;

    -ms-flex: 5 1 auto;

    flex: 5 1 auto; }



.zhi-tabs.zhi-style9.zhi-mobile-layout .zhi-tab-nav {

  border-right: none; }

.zhi-tabs.zhi-style9.zhi-mobile-layout:not(.zhi-mobile-open) .zhi-tab.zhi-active {

  background: #eeeeee;

  border: none; }



/* -------- Vertical Style 10 ----------- */

.zhi-tabs.zhi-style10 .zhi-tab-nav {

  background: #3c3d41;

  border-radius: 5px 0 0 5px; }

  .zhi-tabs.zhi-style10 .zhi-tab-nav .zhi-tab {

    position: relative;

    border-bottom: 1px solid #4e4f53;

    padding: 0; }

    .zhi-tabs.zhi-style10 .zhi-tab-nav .zhi-tab:last-child {

      border-bottom: none; }

    .zhi-tabs.zhi-style10 .zhi-tab-nav .zhi-tab a {

      padding: 10px;

      color: #8f8e93;

      -webkit-transition: all .3s ease-in-out 0s;

      transition: all .3s ease-in-out 0s; }

      .zhi-tabs.zhi-style10 .zhi-tab-nav .zhi-tab a:hover, .zhi-tabs.zhi-style10 .zhi-tab-nav .zhi-tab a:focus {

        color: #ccc; }

    .zhi-tabs.zhi-style10 .zhi-tab-nav .zhi-tab.zhi-active:after {

      content: '';

      display: block;

      position: absolute;

      top: 32px;

      right: 0;

      height: 8px;

      margin: 0 auto;

      border-top: 8px solid transparent;

      border-right: 8px solid #f2f2f2;

      border-bottom: 8px solid transparent; }

    .zhi-tabs.zhi-style10 .zhi-tab-nav .zhi-tab span.zhi-icon-wrapper span, .zhi-tabs.zhi-style10 .zhi-tab-nav .zhi-tab span.zhi-image-wrapper {

      margin: 0 auto; }

    .zhi-tabs.zhi-style10 .zhi-tab-nav .zhi-tab span.zhi-tab-title {

      display: none; }

    .zhi-tabs.zhi-style10 .zhi-tab-nav .zhi-tab.zhi-active a {

      color: #fff; }

.zhi-tabs.zhi-style10 .zhi-tab-panes {

  background: #f2f2f2;

  border-radius: 0 5px 5px 0; }



.zhi-tabs.zhi-style10.zhi-mobile-layout {

  -webkit-box-orient: horizontal;

  box-orient: horizontal;

  -webkit-box-direction: normal;

  box-direction: normal;

  -moz-flex-direction: row;

  flex-direction: row;

  -ms-flex-direction: row; }

  .zhi-tabs.zhi-style10.zhi-mobile-layout .zhi-tab-mobile-menu {

    display: none; }

  .zhi-tabs.zhi-style10.zhi-mobile-layout .zhi-tab-nav .zhi-tab {

    display: block; }



.zhi-dark-bg .zhi-tabs.zhi-style10 .zhi-tab-nav {

  background: #fff; }

  .zhi-dark-bg .zhi-tabs.zhi-style10 .zhi-tab-nav .zhi-tab {

    border-bottom: 1px solid #ececec; }

    .zhi-dark-bg .zhi-tabs.zhi-style10 .zhi-tab-nav .zhi-tab:last-child {

      border-bottom: none; }

    .zhi-dark-bg .zhi-tabs.zhi-style10 .zhi-tab-nav .zhi-tab a {

      color: #969696; }

      .zhi-dark-bg .zhi-tabs.zhi-style10 .zhi-tab-nav .zhi-tab a:hover, .zhi-dark-bg .zhi-tabs.zhi-style10 .zhi-tab-nav .zhi-tab a:focus {

        color: #666; }

    .zhi-dark-bg .zhi-tabs.zhi-style10 .zhi-tab-nav .zhi-tab.zhi-active a {

      color: #333; }

    .zhi-dark-bg .zhi-tabs.zhi-style10 .zhi-tab-nav .zhi-tab.zhi-active:after {

      border-right: 8px solid #e7e7e7; }

.zhi-dark-bg .zhi-tabs.zhi-style10 .zhi-tab-panes {

  background: #e7e7e7; }

  .zhi-dark-bg .zhi-tabs.zhi-style10 .zhi-tab-panes .zhi-tab-pane {

    color: #666; }

    .zhi-dark-bg .zhi-tabs.zhi-style10 .zhi-tab-panes .zhi-tab-pane h1, .zhi-dark-bg .zhi-tabs.zhi-style10 .zhi-tab-panes .zhi-tab-pane h2, .zhi-dark-bg .zhi-tabs.zhi-style10 .zhi-tab-panes .zhi-tab-pane h3, .zhi-dark-bg .zhi-tabs.zhi-style10 .zhi-tab-panes .zhi-tab-pane h4, .zhi-dark-bg .zhi-tabs.zhi-style10 .zhi-tab-panes .zhi-tab-pane h5, .zhi-dark-bg .zhi-tabs.zhi-style10 .zhi-tab-panes .zhi-tab-pane h6 {

      color: #333; }



/*# sourceMappingURL=style.css.map */