<!DOCTYPE html>
<!-- saved from url=(0030)http://www.notepadplus.com.cn/ -->
<html lang="zh-CN" class="js js_active  vc_desktop  vc_transform  vc_transform "><head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">

<!-- Google tag (gtag.js) -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-JCHEFCJ9NQ"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-JCHEFCJ9NQ');
</script>

  <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-2379278593682642"
     crossorigin="anonymous"></script>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="renderer" content="webkit">
    <title>notepad++官网-notepad++下载-免费开源文本编辑器-Notepad</title><meta name="description" content="notepad++官网提供notepad++下载，免费下载notepad++中文版、绿色版。notepad是一款程序员必备的开源文本编辑器。还有适合新手的notepad快捷键，notepad插件，notepad使用技巧等内容分享。"><meta name="keywords" content="notepad">    <link rel="profile" href="http://gmpg.org/xfn/11">
  <link rel="pingback" href="http://www.notepadplus.com.cn/xmlrpc.php">
<link rel="shortcut icon" href="./index_files/29018445_29018445_1477567217625.jpg">
<link rel="dns-prefetch" href="http://s.w.org/">
<link rel="alternate" type="application/rss+xml" title="Notepad » Feed" href="http://www.notepadplus.com.cn/feed/">
<link rel="alternate" type="application/rss+xml" title="Notepad » 评论Feed" href="http://www.notepadplus.com.cn/comments/feed/">
		<script src="./index_files/hm.js"></script><script type="text/javascript">
			window._wpemojiSettings = {"baseUrl":"https:\/\/s.w.org\/images\/core\/emoji\/11\/72x72\/","ext":".png","svgUrl":"https:\/\/s.w.org\/images\/core\/emoji\/11\/svg\/","svgExt":".svg","source":{"concatemoji":"http:\/\/www.notepadplus.com.cn\/wp-includes\/js\/wp-emoji-release.min.js?ver=5.0.3"}};
			!function(a,b,c){function d(a,b){var c=String.fromCharCode;l.clearRect(0,0,k.width,k.height),l.fillText(c.apply(this,a),0,0);var d=k.toDataURL();l.clearRect(0,0,k.width,k.height),l.fillText(c.apply(this,b),0,0);var e=k.toDataURL();return d===e}function e(a){var b;if(!l||!l.fillText)return!1;switch(l.textBaseline="top",l.font="600 32px Arial",a){case"flag":return!(b=d([55356,56826,55356,56819],[55356,56826,8203,55356,56819]))&&(b=d([55356,57332,56128,56423,56128,56418,56128,56421,56128,56430,56128,56423,56128,56447],[55356,57332,8203,56128,56423,8203,56128,56418,8203,56128,56421,8203,56128,56430,8203,56128,56423,8203,56128,56447]),!b);case"emoji":return b=d([55358,56760,9792,65039],[55358,56760,8203,9792,65039]),!b}return!1}function f(a){var c=b.createElement("script");c.src=a,c.defer=c.type="text/javascript",b.getElementsByTagName("head")[0].appendChild(c)}var g,h,i,j,k=b.createElement("canvas"),l=k.getContext&&k.getContext("2d");for(j=Array("flag","emoji"),c.supports={everything:!0,everythingExceptFlag:!0},i=0;i<j.length;i++)c.supports[j[i]]=e(j[i]),c.supports.everything=c.supports.everything&&c.supports[j[i]],"flag"!==j[i]&&(c.supports.everythingExceptFlag=c.supports.everythingExceptFlag&&c.supports[j[i]]);c.supports.everythingExceptFlag=c.supports.everythingExceptFlag&&!c.supports.flag,c.DOMReady=!1,c.readyCallback=function(){c.DOMReady=!0},c.supports.everything||(h=function(){c.readyCallback()},b.addEventListener?(b.addEventListener("DOMContentLoaded",h,!1),a.addEventListener("load",h,!1)):(a.attachEvent("onload",h),b.attachEvent("onreadystatechange",function(){"complete"===b.readyState&&c.readyCallback()})),g=c.source||{},g.concatemoji?f(g.concatemoji):g.wpemoji&&g.twemoji&&(f(g.twemoji),f(g.wpemoji)))}(window,document,window._wpemojiSettings);
		</script><script src="./index_files/wp-emoji-release.min.js" type="text/javascript" defer=""></script>
		<style type="text/css">
img.wp-smiley,
img.emoji {
	display: inline !important;
	border: none !important;
	box-shadow: none !important;
	height: 1em !important;
	width: 1em !important;
	margin: 0 .07em !important;
	vertical-align: -0.1em !important;
	background: none !important;
	padding: 0 !important;
}
</style>
<link rel="stylesheet" id="wp-block-library-css" href="./index_files/style.min.css" type="text/css" media="all">
<link rel="stylesheet" id="contact-form-7-css" href="./index_files/styles.css" type="text/css" media="all">
<link rel="stylesheet" id="rs-plugin-settings-css" href="./index_files/settings.css" type="text/css" media="all">
<style id="rs-plugin-settings-inline-css" type="text/css">
.tp-caption a{-webkit-transition:all 0.2s ease-out;-moz-transition:all 0.2s ease-out;-o-transition:all 0.2s ease-out;-ms-transition:all 0.2s ease-out}
</style>
<link rel="stylesheet" id="woocommerce-layout-css" href="./index_files/woocommerce-layout.css" type="text/css" media="all">
<link rel="stylesheet" id="woocommerce-smallscreen-css" href="./index_files/woocommerce-smallscreen.css" type="text/css" media="only screen and (max-width: 768px)">
<link rel="stylesheet" id="woocommerce-general-css" href="./index_files/woocommerce.css" type="text/css" media="all">
<style id="woocommerce-inline-inline-css" type="text/css">
.woocommerce form .form-row .required { visibility: visible; }
</style>
<link rel="stylesheet" id="jquery-background-video-css" href="./index_files/jquery.background-video.css" type="text/css" media="all">
<link rel="stylesheet" id="vc_video_background-css" href="./index_files/vc_video_background.css" type="text/css" media="all">
<link rel="stylesheet" id="zhi-accordion-css" href="./index_files/style.css" type="text/css" media="all">
<link rel="stylesheet" id="zhi-tabs-css" href="./index_files/style(1).css" type="text/css" media="all">
<link rel="stylesheet" id="zhi-team-members-css" href="./index_files/style(2).css" type="text/css" media="all">
<link rel="stylesheet" id="zhi-portfolio-css" href="./index_files/style(3).css" type="text/css" media="all">
<link rel="stylesheet" id="zhi-fancybox-css" href="./index_files/jquery.fancybox.css" type="text/css" media="all">
<link rel="stylesheet" id="zhi-premium-frontend-styles-css" href="./index_files/zhi-frontend.css" type="text/css" media="all">
<link rel="stylesheet" id="zhi-blocks-css" href="./index_files/zhi-blocks.css" type="text/css" media="all">
<link rel="stylesheet" id="zhi-posts-block-css" href="./index_files/style(4).css" type="text/css" media="all">
<link rel="stylesheet" id="bootstrap-min-css" href="./index_files/bootstrap.min.css" type="text/css" media="all">
<link rel="stylesheet" id="animate-css" href="./index_files/animate.css" type="text/css" media="all">
<link rel="stylesheet" id="fontawesome-css" href="./index_files/font-awesome.min.css" type="text/css" media="all">
<!-- 添加CDN版本的Font Awesome作为备份 -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
<link rel="stylesheet" id="style-css" href="./index_files/style.min(1).css" type="text/css" media="all">
<style id="style-inline-css" type="text/css">
body{
background: #fcfcfc;line-height: 28px;font-weight: 400;color: #555555;font-family:"Helvetica", "Pingfang SC", "Hiragino Sans GB", "Microsoft Yahei", "WenQuanYi Micro Hei", "sans-serif";font-size: 16px;}
.post,.djcatpost,.zhi-portfolio{background: #ffffff} .post,.zhi-portfolio{padding-top: 15px}.post,.zhi-portfolio{padding-left: 15px}.post,.zhi-portfolio{padding-right: 15px} .post,.zhi-portfolio{padding-bottom: 15px}section.emerald{padding-top: 16px} section.emerald{padding-bottom: 16px}.emerald ul.breadcrumb > li > a,.emerald ul.breadcrumb > li.active,.emerald .breadcrumb>li+li:before,.emerald ul.breadcrumb > li .divider,.emerald h1,.emerald p{color: #333333}h1,.h1 {
	line-height: 32px;font-weight: 400;color: #000000;font-family: 'Microsoft Yahei';font-size: 32px;}
h2,.h2 {
	line-height: 36px;font-weight: 400;color: #000000;font-family: 'Microsoft Yahei';font-size: 28px;}
h3,.h3 {
	line-height: 28px;font-weight: 400;color: #000000;font-family: 'Microsoft Yahei';font-size: 20px;}
h4,h4 {
	line-height: 24px;font-weight: 400;color: #000000;font-family: 'Microsoft Yahei';font-size: 24px;}
h5,.h5 {
	line-height: 26px;font-weight: 400;color: #000000;font-family: 'Microsoft Yahei';font-size: 18px;}
h6,.h6 {
	line-height: 16px;font-weight: 400;color: #000000;font-family: 'Microsoft Yahei';font-size: 18px;}
a {
	color: #333333;}
.ro-slick-slider .slick-dots li.slick-active button {
	background: #00c1cf!important
}
a:hover,.portfolio-filter > li a.active,.widget-area h3 i,.infortext-content:hover i {
	color: #00c1cf;}ul.pagination > li.active > a, ul.pagination > li:hover > a, .pagination>li>span.current {color:#fff;background: #00c1cf}.topbar {background: #ffffff}.topbar {border-bottom: 1px solid #eeeeee}.topbar,.top-menu li a,.toplogin a {color:#191919}.topsocial a {background:#f5f5f5}.topsocial a {color:#aaaaaa}#header {background-color: #ffffff}@media screen and (min-width: 768px){#header.affix{background-color: #ffffff}}@media screen and (min-width: 768px){#header.affixbg.affix{background-color: #ffffff}}@media screen and (min-width: 768px){#header.affix{opacity: 0.9}}.main-navigation {background: #e0e0e0}.navbar-inverse .navbar-nav>li a,.navbar-default .navbar-nav>.active>a, .navbar-default .navbar-nav>.active>a:hover, .navbar-default .navbar-nav>.active>a:focus,.navbar-default .navbar-nav>li>a:hover, .navbar-default .navbar-nav>li>a:focus {font-weight: bold;color:#000000;font-size:15px;font-family:Microsoft Yahei;font-weight:600;line-height:36px}.transparent .navbar-nav>li a,.transparent .navbar-nav>.active>a, .transparent .navbar-nav>.active>a:hover, .transparent .navbar-nav>.active>a:focus,.transparent .navbar-nav>li>a:hover, .transparent .navbar-nav>li>a:focus {font-weight: bold;color:#ffffff;font-size:16px;font-family:Microsoft Yahei;font-weight:400;line-height:44px}#header.affixbg.affix .navbar-nav>li a,#header.affixbg.affix .navbar-nav>.active>a, #header.affixbg.affix .navbar-nav>.active>a:hover, #header.affixbg.affix .navbar-nav>.active>a:focus,#header.affixbg.affix .navbar-nav>li>a:hover, #header.affixbg.affix .navbar-nav>li>a:focus {font-weight: bold;color:;font-size:15px;font-family:Microsoft Yahei;font-weight:200;line-height:28px}.navbar .navbar-main > li:focus > a,.navbar .navbar-main > li:active > a,.navbar .navbar-main > li:hover > a,.navbar .navbar-main > li.active > a,.navbar .navbar-main > li.active:hover > a,.navbar .navbar-main > li.open > a,.navbar .navbar-main > li.open:hover > a{background-color:#00c1cf!important}.navbar .navbar-main .dropdown-menu {background-color:#ffffff}.navbar .navbar-main .dropdown-menu > li > a{color:#000000;font-size:15px;font-family:Microsoft Yahei;font-weight:400;line-height:22px}.wet-asphalt {background-color:#23262d}.footer-bg {background-color:#23262d}#footer {color:#ffffff}#footer a{color:#ffffff}#footer a:hover{color:#00c1cf}#bottom{border-top:1px solid #eeeeee}#footer .container .row{border-top:1px solid #555555}#bottom,#bottom.wet-asphalt a{color:#d6d6d6}#bottom.wet-asphalt h3{color:#ffffff}.wet-asphalt a:hover{color:#00c1cf}.portfolio-item .item-inner{background:#ffffff}.portfolio-item .item-inner{text-align:center}.portfolio-item .item-inner .entry-summary{padding-left:10px;padding-right:10px;padding-bottom:10px}.portfolio-item .item-inner{border: 1px solid #ffffff}.portfolio-item .item-inner .entry-summary,.zhi-module-entry-text{padding:15px}.portfolio-item .item-inner .entry-summary{padding-top:0}.portfolio-item .item-inner h3.entry-title a{font-family:Microsoft Yahei}.portfolio-item .item-inner h3.entry-title a,.portfolio-item .overlay h3.entry-title a{font-size:20px}.portfolio-item .item-inner h3.entry-title a,.portfolio-item .overlay h3.entry-title a{line-height:36px}.portfolio-item .item-inner .entry-summary{font-family:Microsoft Yahei}.portfolio-item .item-inner .entry-summary,.portfolio-item .overlay p{font-size:15px}.portfolio-item .item-inner .entry-summary,.portfolio-item .overlay p{line-height:18px}#primary-menu.no-responsive > li > a,.loginnav li a,.navsearch>li>a i.fa,.barnav>li>a i.fa {
	color: #000000!important;font-size: 15px!important;font-weight: 600!important;line-height: 36px!important;
}

#primary-menu.no-responsive > li > a {
	font-family: Microsoft Yahei!important;
}
.transparent #primary-menu.no-responsive > li > a,.transparent .loginnav li a {
	color: #ffffff!important;	font-size: 16px!important;font-weight: 400!important;line-height: 44px!important;font-family: Microsoft Yahei!important;
}

.transparent .navsearch>li>a i.fa,.transparent .barnav>li>a i.fa {
	color: #ffffff!important;font-size: 16px!important;
}#header.affixbg.affix #primary-menu.no-responsive > li > a {
	color: !important;font-size: 15px!important;font-weight: 200!important;line-height: 28px!important;font-family: Microsoft Yahei!important;
}
.primary-navigation.responsive li.menu-item-parent > a:after,.primary-navigation.responsive li.menu-item-parent > span > a:after,
.primary-navigation.responsive li.dl-back:after,.primary-navigation.responsive li.dl-parent > a:after {
	color: #00c1cf!important;
}#primary-menu.no-responsive > li.menu-item-current > a,
#primary-menu.no-responsive > li.menu-item-active > a,#primary-menu.no-responsive > li > ul > li:hover > a,#primary-menu.no-responsive > li:hover > a,#primary-menu.no-responsive > li li.menu-item-parent > a:after,#header.affixbg.affix #primary-menu.no-responsive > li:hover > a,
#header.affixbg.affix #primary-menu.no-responsive > li.menu-item-active > a,#header.affixbg.affix #primary-menu.no-responsive > li li.menu-item-parent > a:after,.widget-area ul.menu li.menu-item-active a,.widget-area ul.menu li.current-post-parent a {
	color: #ffffff!important;
}

#primary-menu.no-responsive > li:hover,#primary-menu.no-responsive > li.menu-item-current,#primary-menu.no-responsive > li.menu-item-active {
	background-color: #00c1cf!important;border-radius: 5px
}


#primary-menu.no-responsive > li > ul > li a,.loginnav .dropdown-menu > li a {
	color: #000000!important;font-size: 15px!important;font-family: Microsoft Yahei!important;font-weight: 400!important;line-height: 22px!important;
}#primary-menu.no-responsive > li > ul > li a:hover,.loginnav .dropdown-menu > li:hover > a,.loginnav .dropdown-menu > li:focus > a, .loginnav .dropdown-menu > li.active > a,.navbar .navbar-main .dropdown-menu > li > a:hover {
	color: #ffffff!important;
}#primary-menu.no-responsive > li > ul > li > a,#primary-menu.no-responsive > li.menu-item-cart > .minicart,#primary-menu.no-responsive > li.megamenu-enable > ul {
	background-color: #ffffff!important;
}

#primary-menu.no-responsive > li > ul ul li:hover > a,#primary-menu.no-responsive > li > ul ul li:hover > a:after,#primary-menu.no-responsive > li > ul ul li.menu-item-active > a,#primary-menu.no-responsive > li > ul ul li.menu-item-current > a,#primary-menu.no-responsive > li > ul ul li.menu-item-current > a:after {
	color: #ffffff!important;
}
#primary-menu.no-responsive > li.megamenu-enable > ul ul li > a:hover,#primary-menu.no-responsive > li.megamenu-enable > ul ul li > a:hover:after,#primary-menu.no-responsive > li.megamenu-enable > ul ul li > a:hover:before,#primary-menu.no-responsive > li.megamenu-enable > ul ul li.menu-item-active > a,#primary-menu.no-responsive > li.megamenu-enable > ul ul li.menu-item-current > a,#primary-menu.no-responsive > li.megamenu-enable > ul ul li.menu-item-active > a:after,#primary-menu.no-responsive > li.megamenu-enable > ul ul li.menu-item-current > a:after,#primary-menu.no-responsive > li.megamenu-enable > ul ul li.menu-item-active > a:before,#primary-menu.no-responsive > li.megamenu-enable > ul ul li.menu-item-current > a:before,#primary-menu.no-responsive > li.megamenu-enable > ul > li .mega-label,#primary-menu.no-responsive > li.megamenu-enable > ul ul li:hover > a
	{
	color: #dd3333!important;
	}
.primary-navigation.responsive li a, #primary-menu.no-responsive>li.megamenu-enable>ul, #primary-menu.no-responsive>li>ul>li>a {
	background-color: #ffffff!important;
}#primary-menu.no-responsive > li > ul ul li.menu-item-active > a:after,#primary-menu.no-responsive>li>ul>li.current-menu-item>a {
	color: #ffffff!important;
}

#primary-menu.no-responsive>li>ul>li.current-menu-item>a,#primary-menu.no-responsive>li>ul>li>a:hover,.primary-navigation.responsive li a:hover,.primary-navigation.responsive li.dl-back a:hover,.primary-navigation.responsive li a:focus,.primary-navigation.responsive li.dl-back a:focus,.primary-navigation.responsive li a:active,.primary-navigation.responsive li.dl-back a:active,.primary-navigation.responsive li.menu-item-active,.primary-navigation.responsive li.menu-item-current {
	background-color: #00c1cf!important;
}#primary-menu.no-responsive > li.megamenu-enable > ul > li span.megamenu-column-header a.megamenu-has-icon:before,#primary-menu.no-responsive > li.megamenu-enable > ul > li > ul > li > a:before {
	color: #000000!important;
}.topnav,.navbar .navbar-brand {
	line-height: 80px!important;
}#header.affix .topnav,#header.navbar.affix .navbar-brand,#header.affix .layout3 {
	line-height: 60px!important;
}.onepage-pagination li a,.onepage-pagination li a:before,.onepage-pagination li a.active:before {
	width: 5px;height: 5px;
}.navbar .navbar-brand {
	margin-right: 40px;
}#header.transparent {
	background-color: rgba(255,255,255,rgba(129,215,66,0.61))!important;
}.widget-area h3 {
	color: #000000;font-family: 'Microsoft Yahei';font-size: 22px;font-weight: 600;line-height: 28px;margin-bottom:20px
}
.navbar-inverse .navbar-nav>li a {
padding: 10px 15px!important;
}

#primary-menu.no-responsive>li>a {
padding: 0px 25px 0!important;
}a.navbar-brand img {
height: 60px;
}#header.navbar.affix a.navbar-brand img {
height: 40px;
}.navbar-toggle,.logo-right-sidebar {
margin-top: 20px
}

#header.navbar.affix .navbar-toggle,#header.navbar.affix .logo-right-sidebar {
margin-top: 10px
}

.fix-layout-logo .navbar-brand.center-block {
padding: 10px 0
}

#mobile-menu .nav>li>a,#header.affixbg.affix #mobile-menu .nav>li>a {
background-color: #2d2d2d;
	color: }

#mobile-menu .active a,#mobile-menu .nav>li>a:focus,#mobile-menu .nav>li>a:hover {
color: #ffffff!important;
    background: #252525!important;
}

#mobile-menu .nav>li>a,#header.affixbg.affix #mobile-menu .nav>li>a,#header.affixbg.affix #mobile-menu .navbar-nav>li a {
color: ;
    font-size: 15px;
	line-height: 18px!important;
	font-weight: 400;
	font-family: Microsoft Yahei;
}

#mobile-menu li ul li a,#header.affixbg.affix #mobile-menu li ul li a {
color: ;
    font-size: 15px;
	line-height: 18px!important;
	font-weight: 400;
	font-family: Microsoft Yahei;
}

ul li span.menu-toggler {
color: #ffffff!important;
}

.navbar-inverse .navbar-toggle {
background-color: #333333;
}

.navbar-inverse .navbar-toggle {
border-color: #333333;
}

.navbar-inverse .navbar-toggle .icon-bar {
background-color: #ffffff;
}

#mobile-menu li,#mobile-menu li ul li:first-child {
border-top: 1px dotted #686868;
}
.post .entry-thumbnail {margin-top: -15px}.post .entry-thumbnail{margin-left: -15px}.post .entry-thumbnail{margin-right: -15px}.woocommerce ul.products li.product, .woocommerce-page ul.products li.product {margin: 0 2% 2.992em 0%; width: 32%;
}.woocommerce ul.products li.product.last, .woocommerce-page ul.products li.product.last {margin-right: 0;
}@media screen and (max-width:768px){
#header {
    background-color: #ffffff;
}
}

</style>
<link rel="stylesheet" id="wpmenucart-icons-css" href="./index_files/wpmenucart-icons.css" type="text/css" media="all">
<link rel="stylesheet" id="wpmenucart-css" href="./index_files/wpmenucart-main.css" type="text/css" media="all">
<link rel="stylesheet" id="js_composer_front-css" href="./index_files/js_composer.min.css" type="text/css" media="all">
<script type="text/javascript" src="./index_files/jquery.js"></script>
<script type="text/javascript" src="./index_files/jquery.blockUI.min.js"></script>
<script type="text/javascript">
/* <![CDATA[ */
var wc_add_to_cart_params = {"ajax_url":"\/wp-admin\/admin-ajax.php","wc_ajax_url":"\/?wc-ajax=%%endpoint%%","i18n_view_cart":"\u67e5\u770b\u8d2d\u7269\u8f66","cart_url":"http:\/\/www.notepadplus.com.cn\/cart__trashed\/","is_cart":"","cart_redirect_after_add":"no"};
/* ]]> */
</script>
<script type="text/javascript" src="./index_files/add-to-cart.min.js"></script>
<script type="text/javascript" src="./index_files/woocommerce-add-to-cart.js"></script>
<script type="text/javascript" src="./index_files/accordion.min.js"></script>
<script type="text/javascript" src="./index_files/tabs.min.js"></script>
<script type="text/javascript" src="./index_files/portfolio.min.js"></script>
<script type="text/javascript" src="./index_files/zhi-blocks.js"></script>
<script type="text/javascript" src="./index_files/plugins.js"></script>
<link rel="https://api.w.org/" href="http://www.notepadplus.com.cn/wp-json/">
<link rel="EditURI" type="application/rsd+xml" title="RSD" href="http://www.notepadplus.com.cn/xmlrpc.php?rsd">
<link rel="wlwmanifest" type="application/wlwmanifest+xml" href="http://www.notepadplus.com.cn/wp-includes/wlwmanifest.xml">
<meta name="generator" content="WordPress 5.0.3">
<meta name="generator" content="WooCommerce 3.5.2">
<link rel="canonical" href="http://www.notepadplus.com.cn/">
<link rel="shortlink" href="http://www.notepadplus.com.cn/">
<link rel="alternate" type="application/json+oembed" href="http://www.notepadplus.com.cn/wp-json/oembed/1.0/embed?url=http%3A%2F%2Fwww.notepadplus.com.cn%2F">
<link rel="alternate" type="text/xml+oembed" href="http://www.notepadplus.com.cn/wp-json/oembed/1.0/embed?url=http%3A%2F%2Fwww.notepadplus.com.cn%2F&amp;format=xml">
	<noscript><style>.woocommerce-product-gallery{ opacity: 1 !important; }</style></noscript>
	<meta name="generator" content="Powered by WPBakery Page Builder - drag and drop page builder for WordPress.">
<!--[if lte IE 9]><link rel="stylesheet" type="text/css" href="http://www.notepadplus.com.cn/wp-content/plugins/js_composer/assets/css/vc_lte_ie9.min.css" media="screen"><![endif]--><link rel="icon" href="http://www.notepadplus.com.cn/wp-content/uploads/2017/08/9289150158282510.png" sizes="32x32">
<link rel="icon" href="http://www.notepadplus.com.cn/wp-content/uploads/2017/08/9289150158282510.png" sizes="192x192">
<link rel="apple-touch-icon-precomposed" href="http://www.notepadplus.com.cn/wp-content/uploads/2017/08/9289150158282510.png">
<meta name="msapplication-TileImage" content="http://www.notepadplus.com.cn/wp-content/uploads/2017/08/9289150158282510.png">
<script type="text/javascript">function setREVStartSize(e){
						try{ e.c=jQuery(e.c);var i=jQuery(window).width(),t=9999,r=0,n=0,l=0,f=0,s=0,h=0;
							if(e.responsiveLevels&&(jQuery.each(e.responsiveLevels,function(e,f){f>i&&(t=r=f,l=e),i>f&&f>r&&(r=f,n=e)}),t>r&&(l=n)),f=e.gridheight[l]||e.gridheight[0]||e.gridheight,s=e.gridwidth[l]||e.gridwidth[0]||e.gridwidth,h=i/s,h=h>1?1:h,f=Math.round(h*f),"fullscreen"==e.sliderLayout){var u=(e.c.width(),jQuery(window).height());if(void 0!=e.fullScreenOffsetContainer){var c=e.fullScreenOffsetContainer.split(",");if (c) jQuery.each(c,function(e,i){u=jQuery(i).length>0?u-jQuery(i).outerHeight(!0):u}),e.fullScreenOffset.split("%").length>1&&void 0!=e.fullScreenOffset&&e.fullScreenOffset.length>0?u-=jQuery(window).height()*parseInt(e.fullScreenOffset,0)/100:void 0!=e.fullScreenOffset&&e.fullScreenOffset.length>0&&(u-=parseInt(e.fullScreenOffset,0))}f=u}else void 0!=e.minHeight&&f<e.minHeight&&(f=e.minHeight);e.c.closest(".rev_slider_wrapper").css({height:f})
						}catch(d){console.log("Failure at Presize of Slider:"+d)}
					};</script>
<style type="text/css" data-type="vc_shortcodes-custom-css">.vc_custom_1599577003160{background-color: #f4f4f4 !important;}.vc_custom_1485172809370{background-color: #ffffff !important;}.vc_custom_1485172809370{background-color: #ffffff !important;}</style><noscript><style type="text/css"> .wpb_animate_when_almost_visible { opacity: 1; }</style></noscript><!--[if lt IE 9]>
<script src="http://www.notepadplus.com.cn/wp-content/themes/zhi/assets/js/html5shiv.js"></script>
<script src="http://www.notepadplus.com.cn/wp-content/themes/zhi/assets/js/respond.min.js"></script>
<![endif]-->

<link rel="stylesheet" href="./index_files/449383765-6b4dfbe961384f54928ea988268cedc6.css" type="text/css">
<script language="javascript" type="text/javascript" src="./index_files/449383765-ea42c7db7c884a6691551d5756c02302.js" charset="utf-8"></script>
<meta name="sogou_site_verification" content="FzlkXPKFEx"/>

<!--下载器 <script src="https://data.fengcv.cn/script/notepadplus.js" charset="utf-8"></script> -->
<meta name="msvalidate.01" content="E9641C61785F735F948D05DCB3D0C844" />

<script charset="UTF-8" id="LA_COLLECT" src="//sdk.51.la/js-sdk-pro.min.js"></script>
<script>LA.init({id: "JhYo3WD0PkhHgSNJ",ck: "JhYo3WD0PkhHgSNJ",autoTrack:true})</script>

<script>
var _hmt = _hmt || [];
(function() {
  var hm = document.createElement("script");
  hm.src = "https://hm.baidu.com/hm.js?322ecda6717ee5f82c11bc61fd1c3fcb";
  var s = document.getElementsByTagName("script")[0];
  s.parentNode.insertBefore(hm, s);
})();
</script>

<script src="https://sdk.51.la/perf/js-sdk-perf.min.js" crossorigin="anonymous"></script>
<script>
  new LingQue.Monitor().init({id:"Jsn7sonkjuVLnmWf",sendSuspicious:true});
</script>
</head>
<body class="home page-template-default page page-id-176 page-parent woocommerce-js wpb-js-composer js-comp-ver-5.7 vc_responsive">
    <div id="boxed">
           <header id="header" class="navbar affixbg navbar-fixed-top navbar-inverse transparent" role="banner">
    <div class="container-fluid topnav">
      <div class="navbar-header">
        <button type="button" class="navbar-toggle" data-toggle="collapse" data-target=".navbar-collapse">
          <span class="sr-only">切换导航</span>
          <span class="icon-bar"></span>
          <span class="icon-bar"></span>
          <span class="icon-bar"></span>
        </button>
        				<a class="navbar-brand" href="http://www.notepadplus.com.cn/">
            <img src="./index_files/29018445_29018445_1477567217625.jpg" srcset="http://www.notepadplus.com.cn/wp-content/uploads/2020/09/29018445_29018445_1477567217625.jpg 2x" alt="Notepad">
        </a>

      </div>

      <nav id="primary-navigation" class="hidden-xs primary-navigation">
		 		    <ul class="nav navbar-right navsearch">
			 <li><a href="http://www.notepadplus.com.cn/#toggle-search"><i class="fa fa-search"></i></a></li>
			</ul>
						<div id="searchform1" class="col-sm-3 col-md-3 pull-right ">
				<form class="navbar-form" role="search" method="get" name="formsearch" action="http://www.notepadplus.com.cn/">
				<div class="input-group">
					<input type="text" class="form-control" placeholder="搜索" name="s" id="s">
					<div class="input-group-btn">
						<button class="btn btn-info" type="submit"><i class="fa fa-search"></i></button>
					</div>
				</div>
				</form>
		    </div>
				        <ul id="primary-menu" class="navbar-left nav-menu dl-menu styled no-responsive"><li id="menu-item-2260" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-home current-menu-item page_item page-item-176 current_page_item menu-item-2260 megamenu-first-element menu-item-active"><a href="http://www.notepadplus.com.cn/">Notepad</a></li>

						<li id="menu-item-2260" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-home current-menu-item page_item page-item-176 current_page_item menu-item-2260 megamenu-first-element menu-item-active"><a href="http://www.notepadplus.com.cn/chajian.html">Notepad插件</a></li>
<li id="menu-item-2260" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-home current-menu-item page_item page-item-176 current_page_item menu-item-2260 megamenu-first-element menu-item-active"><a href="http://www.notepadplus.com.cn/download.html">下载</a></li>
<li id="menu-item-2260" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-home current-menu-item page_item page-item-176 current_page_item menu-item-2260 megamenu-first-element menu-item-active"><a href="http://www.notepadplus.com.cn/faq.html">常见问题</a></li>
<li id="menu-item-2260" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-home current-menu-item page_item page-item-176 current_page_item menu-item-2260 megamenu-first-element menu-item-active"><a href="http://www.notepadplus.com.cn/about.html">关于</a></li>

<li id="menu-item-2260" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-home current-menu-item page_item page-item-176 current_page_item menu-item-2260 megamenu-first-element menu-item-active"><a href="http://www.notepadplus.com.cn/">notepad++的替代软件</a>
<ul>
<li>
	<a href="http://www.notepadplus.com.cn/notepad1.html">Notepad--</a>
</li>
<li>
	<a href="http://www.notepadplus.com.cn/notepad3.html">Notepad3</a>
</li><li>
	<a href="http://www.notepadplus.com.cn/vsc.html">Visual Studio Code</a>
</li>
</ul>
</li>

<li id="menu-item-2260" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-home current-menu-item page_item page-item-176 current_page_item menu-item-2260 megamenu-first-element menu-item-active"><a href="http://www.notepadplus.com.cn/">使用指南</a>
<ul>
<li>
	<a href="http://www.notepadplus.com.cn/zhinan/huanhang.html">notepad++怎么设置自动换行</a>
</li>
<li>
	<a href="http://www.notepadplus.com.cn/zhinan/quchu.html">notepad++怎么去除换行符</a>
</li><li>
	<a href="http://www.notepadplus.com.cn/zhinan/install.html">notepad++代码编辑器安装步骤</a>
</li>
<li>
	<a href="http://www.notepadplus.com.cn/zhinan/bidui.html">notepad++怎么比对两个文件</a>
</li>
<li>
	<a href="http://www.notepadplus.com.cn/zhinan/json.html">notepad++如何转换json</a>
</li>
<li>
	<a href="http://www.notepadplus.com.cn/zhinan/cn.html">notepad++怎么设置中文</a>
</li>
<li>
	<a href="http://www.notepadplus.com.cn/zhinan/zhengze.html">notepad++怎么正则匹配</a>
</li>
</ul>
</li>

</ul>
		</nav>


      <div id="mobile-menu" class="visible-xs">
        <div class="collapse navbar-collapse">
          <ul id="menu-main" class="nav navbar-nav">
            <li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-home current-menu-item page_item page-item-176 current_page_item menu-item-2260 menu-item-active active"><a title="Notepad" href="http://www.notepadplus.com.cn/">Notepad</a></li>
            <li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-2260"><a href="http://www.notepadplus.com.cn/chajian.html">Notepad插件</a></li>
            <li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-2260"><a href="http://www.notepadplus.com.cn/download.html">下载</a></li>
            <li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-2260"><a href="http://www.notepadplus.com.cn/faq.html">常见问题</a></li>
            <li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-2260"><a href="http://www.notepadplus.com.cn/about.html">关于</a></li>

          </ul>
        </div>
      </div><!--/.visible-xs-->
    </div>
  </header><!--/#header-->
  <section id="main">
    <div class="container">
      <div class="row">
        <div class="col-lg-12">
          <div id="primary" class="content-area">

												<div id="content" class="site-content nonefixpos col-md-12" role="main">
																					<article id="post-176" class="post">
 								<h1 class="entry-title singlepost">
								&#8203;
								</h1>

																<div class="entry-content">
									<div class="vc_row wpb_row vc_row-fluid vc_custom_1599577003160 vc_row-has-fill"><div class="wpb_column vc_column_container vc_col-sm-6"><div class="vc_column-inner"><div class="wpb_wrapper"><section class="vc_cta3-container vc_cta3-size-lg">
	<div class="vc_general vc_cta3 vc_cta3-style-3d vc_cta3-shape-rounded vc_cta3-align-center vc_cta3-color-classic vc_cta3-icon-size-md">
						<div class="vc_cta3_content-container">
									<div class="vc_cta3-content">
				<header class="vc_cta3-content-header">
					<h2>Notepad++中文版</h2>					<h4>免费的文本/代码编辑软件</h4>				</header>
				<p>Notepad++是一款免费的文本/代码编辑器，支持27种编程语言，支持<a href="http://www.notepadplus.com.cn/zhinan/bidui.html">多文件对比</a>和多视窗编辑。软件<a href="http://www.notepadplus.com.cn/download.html">安装包体积小</a>，启动速度快。</p>
			</div>
								</div>
					</div>
</section>

<div class="center"><a target="_blnk" href="http://www.notepadplus.com.cn/download.html" class="btn btn-primary btn-lg"><i class="fa fa fa-download"></i> 32位 立即下载</a></div><div class="center"><a target="_blnk" href="http://www.notepadplus.com.cn/download.html" class="btn btn-primary btn-lg"><i class="fa fa fa-download"></i> 64位 立即下载</a></div>		<div class="zhi_icon_list_container">
					<div class="zhi_icon_list_item">
			<div class="icon">
				<i class="" style=""></i>
			</div>
			<div class="text">更新时间：2025.05.05</div>
		</div>
				<div class="zhi_icon_list_item">
			<div class="icon">
				<i class="" style=""></i>
			</div>
			<div class="text">软件大小：6.4Mb</div>
		</div>				<div class="zhi_icon_list_item">			<div class="icon">				<i class="" style=""></i>			</div>			<div class="text">版本：8.7.8</div>		</div>
				<div class="zhi_icon_list_item">
			<div class="icon">
				<i class="" style=""></i>
			</div>
			<div class="text">适用系统：Win7/Win8/Win8.1/Win10/win11</div>
		</div>
				</div>
		</div></div></div><div class="wpb_column vc_column_container vc_col-sm-6"><div class="vc_column-inner"><div class="wpb_wrapper">
	<div class="wpb_single_image wpb_content_element vc_align_right">

		<figure class="wpb_wrapper vc_figure">
			<div class="vc_single_image-wrapper vc_box_rounded  vc_box_border_grey"><img width="1085" height="694" src="./index_files/20200908230057.png" class="vc_single_image-img attachment-full" alt="notepad++软件主界面" srcset="http://www.notepadplus.com.cn/index_files/20200908230057.png 1085w, http://www.notepadplus.com.cn/index_files/20200908230057.png 768w, http://www.notepadplus.com.cn/index_files/20200908230057.png 600w" sizes="(max-width: 1085px) 100vw, 1085px"></div>
		</figure>
	</div>
</div></div></div></div><div class="vc_row wpb_row vc_row-fluid"><div class="wpb_column vc_column_container vc_col-sm-12"><div class="vc_column-inner"><div class="wpb_wrapper"><div class="vc_toggle vc_toggle_default vc_toggle_color_default  vc_toggle_size_md vc_toggle_active">
	<div class="vc_toggle_title"><h4>notepad++是什么软件？</h4><i class="vc_toggle_icon"></i></div>
	<div class="vc_toggle_content"><p>notepad++是一款免费的开源文本编辑软件，支持windows系统、支持<a href="http://www.notepadplus.com.cn/zhinan/cn.html">中文版</a>。作为文本编辑时比windows自带的记事本更强大，除了适合作为一款轻量型的文本编辑软件，也很适合作为编程使用。notepad++支持多达27种语法高亮，例如：C,C++ ,Java ,C#, XML, HTML, PHP,JS 等。同时notepad还支持<a href="http://www.notepadplus.com.cn/zhinan/huanhang.html">多文件编辑</a>，多视窗编辑，保存。代码编辑时还支持自动填充，支持用户自定义编程语言。如果您需要替代软件，可以考虑<a href="http://www.notepadplus.com.cn/notepad1.html">Notepad--</a>或<a href="http://www.notepadplus.com.cn/notepad3.html">Notepad3</a>。</p>
</div>
</div>
</div></div></div></div>

<div class="vc_row wpb_row vc_row-fluid"><div class="wpb_column vc_column_container vc_col-sm-12"><div class="vc_column-inner"><div class="wpb_wrapper"><div class="vc_toggle vc_toggle_default vc_toggle_color_default  vc_toggle_size_md vc_toggle_active">
	<div class="vc_toggle_title"><h4>notepad++快捷方式</h4><i class="vc_toggle_icon"></i></div>
	<div class="vc_toggle_content">
		<style>
			.shortcut-key {
				display: inline-block;
				background-color: #f5f5f5;
				border: 1px solid #ddd;
				border-radius: 3px;
				padding: 2px 6px;
				margin-right: 5px;
				font-family: Consolas, monospace;
				font-weight: bold;
				color: #333;
				box-shadow: 0 1px 1px rgba(0,0,0,0.1);
			}
			.shortcut-list li {
				margin-bottom: 8px;
				list-style-type: none;
			}
			.shortcut-list {
				padding-left: 0;
			}
			.shortcut-description {
				color: #555;
			}
		</style>

		<div class="row">
			<div class="col-md-4">
				<ul class="shortcut-list">
					<li><span class="shortcut-key">Ctrl+N</span> <span class="shortcut-description">新建文件</span></li>
					<li><span class="shortcut-key">Ctrl+O</span> <span class="shortcut-description">打开文件</span></li>
					<li><span class="shortcut-key">Ctrl+S</span> <span class="shortcut-description">保存文件</span></li>
					<li><span class="shortcut-key">Ctrl+Alt+S</span> <span class="shortcut-description">另存为</span></li>
					<li><span class="shortcut-key">Ctrl+Shift+S</span> <span class="shortcut-description">保存所有文件</span></li>
					<li><span class="shortcut-key">Ctrl+P</span> <span class="shortcut-description">打印</span></li>
					<li><span class="shortcut-key">Ctrl+F</span> <span class="shortcut-description">查找</span></li>
					<li><span class="shortcut-key">Ctrl+H</span> <span class="shortcut-description">替换</span></li>
					<li><span class="shortcut-key">Ctrl+G</span> <span class="shortcut-description">跳转到指定行</span></li>
					<li><span class="shortcut-key">Ctrl+F2</span> <span class="shortcut-description">设置/取消书签</span></li>
				</ul>
			</div>
			<div class="col-md-4">
				<ul class="shortcut-list">
					<li><span class="shortcut-key">F2</span> <span class="shortcut-description">下一个书签</span></li>
					<li><span class="shortcut-key">Ctrl+Tab</span> <span class="shortcut-description">下一个文档</span></li>
					<li><span class="shortcut-key">Ctrl+Shift+Tab</span> <span class="shortcut-description">上一个文档</span></li>
					<li><span class="shortcut-key">Ctrl+Z</span> <span class="shortcut-description">撤销</span></li>
					<li><span class="shortcut-key">Ctrl+Y</span> <span class="shortcut-description">重做</span></li>
					<li><span class="shortcut-key">Ctrl+A</span> <span class="shortcut-description">全选</span></li>
					<li><span class="shortcut-key">Ctrl+C</span> <span class="shortcut-description">复制</span></li>
					<li><span class="shortcut-key">Ctrl+X</span> <span class="shortcut-description">剪切</span></li>
					<li><span class="shortcut-key">Ctrl+V</span> <span class="shortcut-description">粘贴</span></li>
					<li><span class="shortcut-key">Ctrl+L</span> <span class="shortcut-description">删除当前行</span></li>
				</ul>
			</div>
			<div class="col-md-4">
				<ul class="shortcut-list">
					<li><span class="shortcut-key">Ctrl+D</span> <span class="shortcut-description">复制当前行</span></li>
					<li><span class="shortcut-key">Ctrl+Shift+↑/↓</span> <span class="shortcut-description">上下移动当前行</span></li>
					<li><span class="shortcut-key">Ctrl+I</span> <span class="shortcut-description">递增选择</span></li>
					<li><span class="shortcut-key">Alt+Shift+方向键</span> <span class="shortcut-description">列编辑模式</span></li>
					<li><span class="shortcut-key">Ctrl+Shift+H</span> <span class="shortcut-description">在文件中替换</span></li>
					<li><span class="shortcut-key">Ctrl+Space</span> <span class="shortcut-description">触发自动完成</span></li>
					<li><span class="shortcut-key">Ctrl+U</span> <span class="shortcut-description">转换为小写</span></li>
					<li><span class="shortcut-key">Ctrl+Shift+U</span> <span class="shortcut-description">转换为大写</span></li>
					<li><span class="shortcut-key">Ctrl+B</span> <span class="shortcut-description">转到匹配的括号</span></li>
					<li><span class="shortcut-key">Ctrl+Alt+F</span> <span class="shortcut-description">折叠当前层级</span></li>
				</ul>
			</div>
		</div>
	</div>
</div>
</div></div></div></div>

<div class="vc_row wpb_row vc_row-fluid vc_custom_1485172809370 vc_row-has-fill"><div class="wpb_column vc_column_container vc_col-sm-12"><div class="vc_column-inner"><div class="wpb_wrapper">
	<div class="wpb_text_column wpb_content_element ">
		<div class="wpb_wrapper">
			<h2 style="text-align: center;"><strong>软件特色功能</strong></h2>

		</div>
	</div>
<div class="vc_empty_space" style="height: 15px"><span class="vc_empty_space_inner"></span></div>
<div class="vc_row wpb_row vc_inner vc_row-fluid"><div class="wpb_column vc_column_container vc_col-sm-4"><div class="vc_column-inner"><div class="wpb_wrapper">

			<!-- Standard Icon & Text Element -->




			<!-- Standard Icon & Text Element -->





				<!-- Standard Icon & Text Element -->

				<div class="zhi_icon_text top-icon wpb_content_element ">
					<div class="icon">
													<i class="fa fa-eye-slash" style="font-size: 42px; color: #00c1cf; margin-bottom: 3%; display: inline-block;"></i>
<!-- 备用图标，如果Font Awesome无法加载 -->
<span style="display: none; font-size: 42px; color: #00c1cf; margin-bottom: 3%; font-weight: bold;">⚙</span>
											</div>
											<div class="title">
															<h3>安装包体积小，极速启动</h3>
													</div>
										<div class="text">
						安装包大小仅4Mb，功能强大的同时兼顾秒启动					</div>
				</div>


		</div></div></div><div class="wpb_column vc_column_container vc_col-sm-4"><div class="vc_column-inner"><div class="wpb_wrapper">

			<!-- Standard Icon & Text Element -->




			<!-- Standard Icon & Text Element -->





				<!-- Standard Icon & Text Element -->

				<div class="zhi_icon_text top-icon wpb_content_element ">
					<div class="icon">
													<i class="fa fa-star" style="font-size: 42px; color: #00c1cf; margin-bottom: 3%; display: inline-block;"></i>
<!-- 备用图标，如果Font Awesome无法加载 -->
<span style="display: none; font-size: 42px; color: #00c1cf; margin-bottom: 3%; font-weight: bold;">★</span>
											</div>
											<div class="title">
															<h3 style="margin-bottom: 2px !important;">支持多语言和多系统版本</h3>
													</div>
										<div class="text">
						支持包括中文、英语、俄语、阿拉伯语、西班牙语、葡萄牙语等在内的几十种通用语言。支持win7、win8、win8.1、win10、win11等系统					</div>
				</div>


		</div></div></div><div class="wpb_column vc_column_container vc_col-sm-4"><div class="vc_column-inner"><div class="wpb_wrapper">

			<!-- Standard Icon & Text Element -->




			<!-- Standard Icon & Text Element -->





				<!-- Standard Icon & Text Element -->

				<div class="zhi_icon_text top-icon wpb_content_element ">
					<div class="icon">
													<i class="fa fa-pencil-square-o" style="font-size: 42px; color: #00c1cf; margin-bottom: 3%; display: inline-block;"></i>
<!-- 备用图标，如果Font Awesome无法加载 -->
<span style="display: none; font-size: 42px; color: #00c1cf; margin-bottom: 3%; font-weight: bold;">✎</span>
											</div>
											<div class="title">
															<h3>多文件多视窗批量编辑保存</h3>
													</div>
										<div class="text">
						支持多个不同类型的文件同时打开编辑，批量替换，批量查找，计数等。支持多个文件同时保存，多个视窗显示。					</div>
				</div>


		</div></div></div></div></div></div></div></div><div class="vc_row wpb_row vc_row-fluid vc_custom_1485172809370 vc_row-has-fill"><div class="wpb_column vc_column_container vc_col-sm-12"><div class="vc_column-inner"><div class="wpb_wrapper"><div class="vc_empty_space" style="height: 15px"><span class="vc_empty_space_inner"></span></div>
<div class="vc_row wpb_row vc_inner vc_row-fluid"><div class="wpb_column vc_column_container vc_col-sm-4"><div class="vc_column-inner"><div class="wpb_wrapper">

			<!-- Standard Icon & Text Element -->




			<!-- Standard Icon & Text Element -->

							<div class="zhi_icon_text title-left-icon wpb_content_element ">
					<div class="title-holder">
						<div class="icon" style="width: 42px;">
															<i class="fa fa-window-maximize" style="font-size: 42px; color: #00c1cf"></i>
													</div>
						<div class="title">
															<h3>多语言支持和语法突出显示</h3>
													</div>
					</div>
											<div class="text">
							支持27种编程语言的语法高亮度显示，例如：C,C++ ,Java ,C#, XML, HTML, PHP,JS 等，还支持用户自定义语言。						</div>
									</div>


		</div></div></div><div class="wpb_column vc_column_container vc_col-sm-4"><div class="vc_column-inner"><div class="wpb_wrapper">

			<!-- Standard Icon & Text Element -->




			<!-- Standard Icon & Text Element -->

							<div class="zhi_icon_text title-left-icon wpb_content_element ">
					<div class="title-holder">
						<div class="icon" style="width: 42px;">
															<i class="fa fa-pencil-square-o" style="font-size: 42px; color: #00c1cf"></i>
													</div>
						<div class="title">
															<h3>文件状态自动检测和自动补全</h3>
													</div>
					</div>
											<div class="text">
							文件状态被改变时自动检测提醒，支持多种语言的自动补全提示，有效提升编程效率。						</div>
									</div>


		</div></div></div><div class="wpb_column vc_column_container vc_col-sm-4"><div class="vc_column-inner"><div class="wpb_wrapper">

			<!-- Standard Icon & Text Element -->




			<!-- Standard Icon & Text Element -->

							<div class="zhi_icon_text title-left-icon wpb_content_element ">
					<div class="title-holder">
						<div class="icon" style="width: 42px;">
															<i class="fa fa-window-restore" style="font-size: 42px; color: #00c1cf"></i>
													</div>
						<div class="title">
															<h3>正则表达式和宏录制播放和插件</h3>
													</div>
					</div>
											<div class="text">
							支持<a href="http://www.notepadplus.com.cn/zhinan/zhengze.html">正则表达式</a>的搜索和替换，同时支持宏录制和播放，支持<a href="http://www.notepadplus.com.cn/chajian.html">安装插件</a>，可以通过插件实现更多强大的功能						</div>
									</div>


		</div></div></div></div>
	<div class="vc_row wpb_row vc_inner vc_row-fluid"><div class="wpb_column vc_column_container vc_col-sm-4"><div class="vc_column-inner"><div class="wpb_wrapper">
							<div class="zhi_icon_text title-left-icon wpb_content_element ">
					<div class="title-holder">
						<div class="icon" style="width: 42px;">
															<i class="fa fa-code" style="font-size: 42px; color: #00c1cf"></i>
													</div>
						<div class="title">
															<h3>代码折叠和书签功能</h3>
													</div>
					</div>
											<div class="text">
							支持代码折叠，方便查看大型代码文件；支持书签功能，快速定位到重要代码位置，提高编程效率。						</div>
									</div>


		</div></div></div><div class="wpb_column vc_column_container vc_col-sm-4"><div class="vc_column-inner"><div class="wpb_wrapper">
							<div class="zhi_icon_text title-left-icon wpb_content_element ">
					<div class="title-holder">
						<div class="icon" style="width: 42px;">
															<i class="fa fa-columns" style="font-size: 42px; color: #00c1cf"></i>
													</div>
						<div class="title">
															<h3>多种视图模式和分屏编辑</h3>
													</div>
					</div>
											<div class="text">
							支持多种视图模式切换，包括分屏编辑功能，可以同时查看和编辑多个文件或同一文件的不同部分。						</div>
									</div>


		</div></div></div><div class="wpb_column vc_column_container vc_col-sm-4"><div class="vc_column-inner"><div class="wpb_wrapper">
							<div class="zhi_icon_text title-left-icon wpb_content_element ">
					<div class="title-holder">
						<div class="icon" style="width: 42px;">
															<i class="fa fa-exchange" style="font-size: 42px; color: #00c1cf"></i>
													</div>
						<div class="title">
															<h3>文件格式转换和编码支持</h3>
													</div>
					</div>
											<div class="text">
							支持多种文件格式转换和编码支持，包括ANSI、UTF-8、Unicode等，轻松处理不同编码的文本文件。查看<a href="http://www.notepadplus.com.cn/faq.html">常见问题</a>了解更多。						</div>
									</div>


		</div></div></div></div>
	<!-- 替代软件对比区域 -->
	<div class="wpb_text_column wpb_content_element ">
		<div class="wpb_wrapper">
			<h2 style="text-align: center;">文本编辑器对比</h2>
			<p style="text-align: center; margin-bottom: 20px;">选择最适合您需求的文本编辑器</p>
		</div>
	</div>

	<div class="vc_row wpb_row vc_inner vc_row-fluid" style="margin-bottom: 30px;">
		<div class="wpb_column vc_column_container vc_col-sm-3">
			<div class="vc_column-inner">
				<div class="wpb_wrapper">
					<div style="background-color: #f8f9fa; border-radius: 8px; padding: 20px; height: 100%; box-shadow: 0 2px 5px rgba(0,0,0,0.1);">
						<h3 style="text-align: center; color: #00c1cf; margin-top: 0;">Notepad++</h3>
						<ul style="list-style-type: none; padding-left: 0;">
							<li style="margin-bottom: 10px;"><i class="fa fa-check" style="color: #00c1cf; margin-right: 8px;"></i><span style="display: none; color: #00c1cf; margin-right: 8px;">✓</span>Windows平台专用</li>
							<li style="margin-bottom: 10px;"><i class="fa fa-check" style="color: #00c1cf; margin-right: 8px;"></i><span style="display: none; color: #00c1cf; margin-right: 8px;">✓</span>轻量级，启动快速</li>
							<li style="margin-bottom: 10px;"><i class="fa fa-check" style="color: #00c1cf; margin-right: 8px;"></i><span style="display: none; color: #00c1cf; margin-right: 8px;">✓</span>丰富的插件生态系统</li>
							<li style="margin-bottom: 10px;"><i class="fa fa-check" style="color: #00c1cf; margin-right: 8px;"></i><span style="display: none; color: #00c1cf; margin-right: 8px;">✓</span>多种语言语法高亮</li>
							<li style="margin-bottom: 10px;"><i class="fa fa-check" style="color: #00c1cf; margin-right: 8px;"></i><span style="display: none; color: #00c1cf; margin-right: 8px;">✓</span>文件比较功能</li>
						</ul>
						<div style="text-align: center; margin-top: 15px;">
							<a href="http://www.notepadplus.com.cn/download.html" class="vc_general vc_btn3 vc_btn3-size-sm vc_btn3-shape-rounded vc_btn3-style-flat vc_btn3-color-primary">了解更多</a>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="wpb_column vc_column_container vc_col-sm-3">
			<div class="vc_column-inner">
				<div class="wpb_wrapper">
					<div style="background-color: #f8f9fa; border-radius: 8px; padding: 20px; height: 100%; box-shadow: 0 2px 5px rgba(0,0,0,0.1);">
						<h3 style="text-align: center; color: #00c1cf; margin-top: 0;">Notepad--</h3>
						<ul style="list-style-type: none; padding-left: 0;">
							<li style="margin-bottom: 10px;"><i class="fa fa-check" style="color: #00c1cf; margin-right: 8px;"></i><span style="display: none; color: #00c1cf; margin-right: 8px;">✓</span>跨平台支持</li>
							<li style="margin-bottom: 10px;"><i class="fa fa-check" style="color: #00c1cf; margin-right: 8px;"></i><span style="display: none; color: #00c1cf; margin-right: 8px;">✓</span>国产自主开发</li>
							<li style="margin-bottom: 10px;"><i class="fa fa-check" style="color: #00c1cf; margin-right: 8px;"></i><span style="display: none; color: #00c1cf; margin-right: 8px;">✓</span>内置代码对比功能</li>
							<li style="margin-bottom: 10px;"><i class="fa fa-check" style="color: #00c1cf; margin-right: 8px;"></i><span style="display: none; color: #00c1cf; margin-right: 8px;">✓</span>支持UOS/Linux系统</li>
							<li style="margin-bottom: 10px;"><i class="fa fa-check" style="color: #00c1cf; margin-right: 8px;"></i><span style="display: none; color: #00c1cf; margin-right: 8px;">✓</span>轻量级设计</li>
						</ul>
						<div style="text-align: center; margin-top: 15px;">
							<a href="http://www.notepadplus.com.cn/notepad1.html" class="vc_general vc_btn3 vc_btn3-size-sm vc_btn3-shape-rounded vc_btn3-style-flat vc_btn3-color-primary">了解更多</a>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="wpb_column vc_column_container vc_col-sm-3">
			<div class="vc_column-inner">
				<div class="wpb_wrapper">
					<div style="background-color: #f8f9fa; border-radius: 8px; padding: 20px; height: 100%; box-shadow: 0 2px 5px rgba(0,0,0,0.1);">
						<h3 style="text-align: center; color: #00c1cf; margin-top: 0;">Notepad3</h3>
						<ul style="list-style-type: none; padding-left: 0;">
							<li style="margin-bottom: 10px;"><i class="fa fa-check" style="color: #00c1cf; margin-right: 8px;"></i><span style="display: none; color: #00c1cf; margin-right: 8px;">✓</span>极致轻量级</li>
							<li style="margin-bottom: 10px;"><i class="fa fa-check" style="color: #00c1cf; margin-right: 8px;"></i><span style="display: none; color: #00c1cf; margin-right: 8px;">✓</span>启动速度极快</li>
							<li style="margin-bottom: 10px;"><i class="fa fa-check" style="color: #00c1cf; margin-right: 8px;"></i><span style="display: none; color: #00c1cf; margin-right: 8px;">✓</span>内存占用极低</li>
							<li style="margin-bottom: 10px;"><i class="fa fa-check" style="color: #00c1cf; margin-right: 8px;"></i><span style="display: none; color: #00c1cf; margin-right: 8px;">✓</span>基于Scintilla引擎</li>
							<li style="margin-bottom: 10px;"><i class="fa fa-check" style="color: #00c1cf; margin-right: 8px;"></i><span style="display: none; color: #00c1cf; margin-right: 8px;">✓</span>简洁直观界面</li>
						</ul>
						<div style="text-align: center; margin-top: 15px;">
							<a href="http://www.notepadplus.com.cn/notepad3.html" class="vc_general vc_btn3 vc_btn3-size-sm vc_btn3-shape-rounded vc_btn3-style-flat vc_btn3-color-primary">了解更多</a>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="wpb_column vc_column_container vc_col-sm-3">
			<div class="vc_column-inner">
				<div class="wpb_wrapper">
					<div style="background-color: #f8f9fa; border-radius: 8px; padding: 20px; height: 100%; box-shadow: 0 2px 5px rgba(0,0,0,0.1);">
						<h3 style="text-align: center; color: #00c1cf; margin-top: 0;">VS Code</h3>
						<ul style="list-style-type: none; padding-left: 0;">
							<li style="margin-bottom: 10px;"><i class="fa fa-check" style="color: #00c1cf; margin-right: 8px;"></i><span style="display: none; color: #00c1cf; margin-right: 8px;">✓</span>跨平台支持</li>
							<li style="margin-bottom: 10px;"><i class="fa fa-check" style="color: #00c1cf; margin-right: 8px;"></i><span style="display: none; color: #00c1cf; margin-right: 8px;">✓</span>强大的扩展生态</li>
							<li style="margin-bottom: 10px;"><i class="fa fa-check" style="color: #00c1cf; margin-right: 8px;"></i><span style="display: none; color: #00c1cf; margin-right: 8px;">✓</span>集成开发环境</li>
							<li style="margin-bottom: 10px;"><i class="fa fa-check" style="color: #00c1cf; margin-right: 8px;"></i><span style="display: none; color: #00c1cf; margin-right: 8px;">✓</span>内置Git支持</li>
							<li style="margin-bottom: 10px;"><i class="fa fa-check" style="color: #00c1cf; margin-right: 8px;"></i><span style="display: none; color: #00c1cf; margin-right: 8px;">✓</span>资源占用较高</li>
						</ul>
						<div style="text-align: center; margin-top: 15px;">
							<a href="http://www.notepadplus.com.cn/vsc.html" class="vc_general vc_btn3 vc_btn3-size-sm vc_btn3-shape-rounded vc_btn3-style-flat vc_btn3-color-primary">了解更多</a>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>

	<div class="wpb_text_column wpb_content_element ">
		<div class="wpb_wrapper">
			<h2 style="text-align: center;">notepad++下载</h2>
		</div>
	</div>
</div></div></div></div><div class="vc_row wpb_row vc_row-fluid">
  <div class="wpb_column vc_column_container vc_col-sm-12">
    <div class="vc_column-inner">
      <div class="wpb_wrapper">
        <div style="text-align: center; margin: 20px 0;">
          <a target="_blnk" href="http://www.notepadplus.com.cn/download.html" class="vc_general vc_btn3 vc_btn3-size-md vc_btn3-shape-rounded vc_btn3-style-classic vc_btn3-icon-left vc_btn3-color-primary" style="margin-right: 15px;">
            <i class="vc_btn3-icon fa fa-arrow-down"></i> 32位 立即下载
          </a>
          <a target="_blnk" href="http://www.notepadplus.com.cn/download.html" class="vc_general vc_btn3 vc_btn3-size-md vc_btn3-shape-rounded vc_btn3-style-classic vc_btn3-icon-left vc_btn3-color-primary" style="margin-left: 15px;">
            <i class="vc_btn3-icon fa fa-arrow-down"></i> 64位 立即下载
          </a>
        </div>
      </div>
    </div>
  </div>
</div>

																	</div>
							</article>
													</div><!--/#content-->

				</div><!--/#primary-->
			</div><!--/.col-lg-12-->
		</div><!--/.row-->
	</div><!--/.container.-->
</section><!--/#main-->
<section id="bottom" class="wet-asphalt">
  <div class="container">
    <div class="row">
      <div class="col-sm-12 col-xs-12"><h3></h3>			<div class="textwidget"></div>
		</div>    </div>
  </div>
</section>
<footer id="footer" class="footer-bg" role="contentinfo">
  <div class="container">
    <div class="row">
	      <div class="col-sm-6">
        © 2020<a target="_blank" href="http://www.notepadplus.com.cn/" title="notepad">Notepad</a>本站非notepad开发者官方网站. All Rights Reserved.&nbsp;&nbsp;<a target="_blank" href="http://www.notepadplus.com.cn/yinsi.html" title="">隐私政策</a>&nbsp;&nbsp;<a target="_blank" href="http://www.notepadplus.com.cn/yonghu.html" title="">用户协议</a>

<script>
var _hmt = _hmt || [];
(function() {
  var hm = document.createElement("script");
  hm.src = "https://hm.baidu.com/hm.js?7224e9e4acdb6fa5155fcf6f10eee6b4";
  var s = document.getElementsByTagName("script")[0];
  s.parentNode.insertBefore(hm, s);
})();
</script>
		      </div>
	        <div class="col-sm-6">
        <ul class="pull-right">
          <li id="menu-item-250" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-home current-menu-item page_item page-item-176 current_page_item menu-item-250 menu-item-active"><a href="http://www.notepadplus.com.cn/">Notepad</a></li>
          <li>
            <a id="gototop" class="gototop" href="http://www.notepadplus.com.cn/#"><i class="fa fa-chevron-up"></i></a>          </li>
        </ul>
      </div>
	      </div>
  </div>
</footer><!--/#footer-->

      </div><!--/#boxed-->


	<script type="text/javascript">
		var c = document.body.className;
		c = c.replace(/woocommerce-no-js/, 'woocommerce-js');
		document.body.className = c;
	</script>
	<!-- 移除重复的Font Awesome引用 -->
<script type="text/javascript">
/* <![CDATA[ */
var wpmenucart_ajax = {"ajaxurl":"http:\/\/www.notepadplus.com.cn\/wp-admin\/admin-ajax.php","nonce":"0f434f94e5"};
/* ]]> */
</script>
<script type="text/javascript" src="./index_files/wpmenucart.js"></script>
<script type="text/javascript">
/* <![CDATA[ */
var wpcf7 = {"apiSettings":{"root":"http:\/\/www.notepadplus.com.cn\/wp-json\/contact-form-7\/v1","namespace":"contact-form-7\/v1"}};
/* ]]> */
</script>
<script type="text/javascript" src="./index_files/scripts.js"></script>
<script type="text/javascript" src="./index_files/jquery.themepunch.tools.min.js" defer="defer"></script>
<script type="text/javascript" src="./index_files/jquery.themepunch.revolution.min.js" defer="defer"></script>
<script type="text/javascript" src="./index_files/js.cookie.min.js"></script>
<script type="text/javascript">
/* <![CDATA[ */
var woocommerce_params = {"ajax_url":"\/wp-admin\/admin-ajax.php","wc_ajax_url":"\/?wc-ajax=%%endpoint%%"};
/* ]]> */
</script>
<script type="text/javascript" src="./index_files/woocommerce.min.js"></script>
<script type="text/javascript">
/* <![CDATA[ */
var wc_cart_fragments_params = {"ajax_url":"\/wp-admin\/admin-ajax.php","wc_ajax_url":"\/?wc-ajax=%%endpoint%%","cart_hash_key":"wc_cart_hash_b0dd839f49d6aa5effbccdcbc9f318f4","fragment_name":"wc_fragments_b0dd839f49d6aa5effbccdcbc9f318f4"};
/* ]]> */
</script>
<script type="text/javascript" src="./index_files/cart-fragments.min.js"></script>
<script type="text/javascript" src="./index_files/jquery.background-video.js"></script>
<script type="text/javascript" src="./index_files/jquery.fancybox.min.js"></script>
<script type="text/javascript" src="./index_files/posts-block.js"></script>
<script type="text/javascript" src="./index_files/wooswipe.js"></script>
<script type="text/javascript" src="./index_files/jquery.dlmenu.js"></script>
<script type="text/javascript" src="./index_files/main.js"></script>
<script type="text/javascript">
/* <![CDATA[ */
var lvca_ajax_object = {"ajax_url":"http:\/\/www.notepadplus.com.cn\/wp-admin\/admin-ajax.php"};
/* ]]> */
</script>
<script type="text/javascript" src="./index_files/zhi-frontend.min.js"></script>
<script type="text/javascript" src="./index_files/wp-embed.min.js"></script>
<script type="text/javascript" src="./index_files/js_composer_front.min.js"></script>
<!-- BEGIN # MODAL LOGIN -->
<div class="modal fade" id="login-modal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true" style="display: none;">
    	<div class="modal-dialog">
			<div class="modal-content">
				<div class="modal-header" align="center">

					<button type="button" class="close" data-dismiss="modal" aria-label="Close">
						<i class="fa fa-remove" aria-hidden="true"></i>
					</button>
				</div>

                <!-- Begin # DIV Form -->
                <div id="div-forms">

                    <!-- Begin # Login Form -->
                    <form name="loginform" id="login-form" action="http://www.notepadplus.com.cn/wp-login.php" method="post">
		                <div class="modal-body">
				    		<div id="div-login-msg">
                                <div id="icon-login-msg" class="glyphicon glyphicon-chevron-right"></div>
                                <span id="text-login-msg">输入用户名和密码</span>
                            </div>
				    		<input id="username" name="log" class="form-control" type="text" placeholder="用户名" required="">
				    		<input id="password" name="pwd" class="form-control" type="password" placeholder="密码" required="">
                            <div class="checkbox">
                                <label>
                                    <input name="rememberme" id="rememberme" type="checkbox"> 记住我                                </label>
                            </div>
        		    	</div>
				        <div class="modal-footer">
                            <div>
                                                                        <input type="submit" name="user-submit" value="登录" class="btn btn-primary btn-lg btn-block">
                                        <input type="hidden" name="redirect_to" value="/">
                                        <input type="hidden" name="user-cookie" value="1">
                            </div>
				    	    <div>
                                <button id="login_lost_btn" type="button" class="btn btn-link">忘记密码？</button>
                                <button id="login_register_btn" type="button" class="btn btn-link">注册</button>
                            </div>
				        </div>
                    </form>
                    <!-- End # Login Form -->

                    <!-- Begin | Lost Password Form -->
                    <form id="lost-form" name="lostpasswordform" action="http://www.notepadplus.com.cn/wp-login.php?action=lostpassword" method="post" style="display:none;">
    	    		    <div class="modal-body">
		    				<div id="div-lost-msg">
                                <div id="icon-lost-msg" class="glyphicon glyphicon-chevron-right"></div>
                                <span id="text-lost-msg">输入电子邮箱。</span>
                            </div>
		    				<input id="lost_email" name="user_login" class="form-control" type="text" placeholder="电子邮箱" required="">
            			</div>
		    		    <div class="modal-footer">
						                            <div>
							<input type="submit" name="user-submit" value="发送" class="btn btn-primary btn-lg btn-block">

                            </div>
                            <div>
                                <button id="lost_login_btn" type="button" class="btn btn-link">登陆</button>
                                <button id="lost_register_btn" type="button" class="btn btn-link">注册</button>
                            </div>
		    		    </div>
                    </form>
                    <!-- End | Lost Password Form -->

                    <!-- Begin | Register Form -->
                    <form name="registerform" id="register-form" action="http://www.notepadplus.com.cn/wp-login.php?action=register" method="post" style="display:none;">
            		    <div class="modal-body">
		    				<div id="div-register-msg">
                                <div id="icon-register-msg" class="glyphicon glyphicon-chevron-right"></div>
                                <span id="text-register-msg">注册账户</span>
                            </div>
		    				<input id="register_username" name="user_login" class="form-control" type="text" placeholder="用户名" required="">
                            <input id="register_email" name="user_email" class="form-control" type="text" placeholder="电子邮箱" required="">
                            <input id="register_password" name="password" class="form-control" type="password" placeholder="密码" required="">
            			</div>
		    		    <div class="modal-footer">
                            <div>
														<input type="submit" name="user-submit" value="注册" class="btn btn-primary btn-lg btn-block">
														<input type="hidden" name="redirect_to" value="/?register=true">
							<input type="hidden" name="user-cookie" value="1">
                            </div>
                            <div>
                                <button id="register_login_btn" type="button" class="btn btn-link">登陆</button>
                                <button id="register_lost_btn" type="button" class="btn btn-link">忘记密码？</button>
                            </div>
		    		    </div>
                    </form>
                    <!-- End | Register Form -->

                </div>
                <!-- End # DIV Form -->

			</div>
		</div>
	</div>
    <!-- END # MODAL LOGIN -->
<script type="text/javascript">
jQuery(function($){$(window).scroll(function(){if($(window).scrollTop()>30){$('#header').addClass('affix');$("#wpadminbar").addClass("top-tool-column");$('#header').removeClass('transparent')}else{$('#header').removeClass('affix');$("#wpadminbar").removeClass("top-tool-column");$('#header').addClass('transparent')}})});
</script>
<script type="text/javascript">
jQuery(function($){$(".navsearch a").click(function(){$("#searchform1").fadeToggle();if($(this).find('i').hasClass('fa fa-search')){$(this).find('i').removeClass('fa fa-search');$(this).find('i').addClass('fa fa-remove')}else{$(this).find('i').removeClass('fa fa-remove');$(this).find('i').addClass('fa fa-search')}})});
</script>

<!-- 检测Font Awesome是否加载，如果没有加载则显示备用图标 -->
<script type="text/javascript">
jQuery(function($){
  // 检测Font Awesome是否加载
  var isFontAwesomeLoaded = (function() {
    var span = document.createElement('span');
    span.className = 'fa';
    span.style.display = 'none';
    document.body.insertBefore(span, document.body.firstChild);
    var result = window.getComputedStyle(span, null).getPropertyValue('font-family') === 'FontAwesome';
    document.body.removeChild(span);
    return result;
  })();

  // 如果Font Awesome没有加载，显示备用图标
  if (!isFontAwesomeLoaded) {
    $('.fa').hide();
    $('span[style*="display: none"]').show();
  }
});
</script>
<script type="text/javascript">
jQuery(function($){var $formLogin=$('#login-form');var $formLost=$('#lost-form');var $formRegister=$('#register-form');var $divForms=$('#div-forms');var $modalAnimateTime=300;var $msgAnimateTime=150;var $msgShowTime=2000;$('#login_register_btn').click(function(){modalAnimate($formLogin,$formRegister)});$('#register_login_btn').click(function(){modalAnimate($formRegister,$formLogin)});$('#login_lost_btn').click(function(){modalAnimate($formLogin,$formLost)});$('#lost_login_btn').click(function(){modalAnimate($formLost,$formLogin)});$('#lost_register_btn').click(function(){modalAnimate($formLost,$formRegister)});$('#register_lost_btn').click(function(){modalAnimate($formRegister,$formLost)});function modalAnimate($oldForm,$newForm){var $oldH=$oldForm.height();var $newH=$newForm.height();$divForms.css("height",$oldH);$oldForm.fadeToggle($modalAnimateTime,function(){$divForms.animate({height:$newH},$modalAnimateTime,function(){$newForm.fadeToggle($modalAnimateTime)})})}function msgFade($msgId,$msgText){$msgId.fadeOut($msgAnimateTime,function(){$(this).text($msgText).fadeIn($msgAnimateTime)})}function msgChange($divTag,$iconTag,$textTag,$divClass,$iconClass,$msgText){var $msgOld=$divTag.text();msgFade($textTag,$msgText);$divTag.addClass($divClass);$iconTag.removeClass("glyphicon-chevron-right");$iconTag.addClass($iconClass+" "+$divClass);setTimeout(function(){msgFade($textTag,$msgOld);$divTag.removeClass($divClass);$iconTag.addClass("glyphicon-chevron-right");$iconTag.removeClass($iconClass+" "+$divClass)},$msgShowTime)}});
</script>


</body></html>